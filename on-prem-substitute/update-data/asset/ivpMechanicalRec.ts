import {
  determineEndpointData,
  EndpointConfig,
  EndpointData,
} from 'update-data/endpointData'
import Context from '../context'
import { BASE_ASSET_URL } from 'src/utils'

/**
 * Hits the ivpMechanicalRec endpoint for various railcars.
 */
const endpointConfig: EndpointConfig = {
  name: 'IVP Mechanical Record',
  baseFilename: 'ivpMechanicalRec',
  url: `${BASE_ASSET_URL}ivpMechanicalRec/:railcar`,
}

const getIVPMechanicalRecData = (context: Context): EndpointData[] => {
  return determineEndpointData(
    endpointConfig,
    'railcar',
    context.railcars,
    'not-a-real-car',
  )
}

export default getIVPMechanicalRecData
