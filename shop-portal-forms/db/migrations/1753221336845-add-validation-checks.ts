import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddValidationChecks1753221336845 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO "validation_check" ("validation_check_code") VALUES 
        ('DecalPresent'),
        ('DecalNotPresent'),
        ('CastingDate')
      `,
    )

    /**
     * Set `DecalPresent` to `Decals - Commodity` question when response is `Present`
     */
    await queryRunner.query(
      `INSERT INTO "dci_response_validation_check" (
        dci_response_pattern_group_id,
        validation_check_id
      )
       SELECT
         rp.dci_response_pattern_group_id,
         vc.validation_check_id
       FROM 
         validation_check           vc,
         dci_response_pattern_group rp,
         dci_question               dq,
         response_option            ro
       WHERE 1=1
        AND dq.dci_question_id = rp.dci_question_id
        AND rp.response_option_id = ro.response_option_id
        AND dq.dci_question = 'Decals - Commodity'
        AND ro.option_name = 'Present'
        AND vc.validation_check_code = 'DecalPresent'
      `,
    )

    /**
     * Set `DecalNotPresent` to `Decals - Commodity` question when response is `Not Present`
     */
    await queryRunner.query(
      `INSERT INTO "dci_response_validation_check" (
        dci_response_pattern_group_id,
        validation_check_id
      )
       SELECT
         rp.dci_response_pattern_group_id,
         vc.validation_check_id
       FROM 
         validation_check           vc,
         dci_response_pattern_group rp,
         dci_question               dq,
         response_option            ro
       WHERE 1=1
        AND dq.dci_question_id = rp.dci_question_id
        AND rp.response_option_id = ro.response_option_id
        AND dq.dci_question = 'Decals - Commodity'
        AND ro.option_name = 'Not Present'
        AND vc.validation_check_code = 'DecalNotPresent'
      `,
    )

    /**
     * Set `CastingDate` to every `DCIResponsePatternGroup` associated to the `IVPCastingDate`
     */
    await queryRunner.query(
      `INSERT INTO "dci_response_validation_check" (
        dci_response_pattern_group_id,
        validation_check_id
      )
       SELECT
         rp.dci_response_pattern_group_id,
         vc.validation_check_id
       FROM 
         validation_check           vc,
         dci_response_pattern_group rp,
         pattern_group              pg
       WHERE 1=1
        AND rp.pattern_group_id = pg.pattern_group_id
        AND pg.pattern_group_code = 'IVPCastingDate'
        AND vc.validation_check_code = 'CastingDate'
      `,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `TRUNCATE TABLE "dci_response_validation_check" CASCADE`,
    )
    await queryRunner.query(`TRUNCATE TABLE "validation_check" CASCADE`)
  }
}
