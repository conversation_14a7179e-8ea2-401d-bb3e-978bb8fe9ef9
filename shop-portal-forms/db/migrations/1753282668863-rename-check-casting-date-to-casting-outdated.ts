import { MigrationInterface, QueryRunner } from 'typeorm'

export class RenameCheckCastingDateToCastingOutdated1753282668863
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "validation_check" SET "validation_check_code" = 'CastingOutdated' WHERE "validation_check_code" = 'CastingDate'`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "validation_check" SET "validation_check_code" = 'CastingDate' WHERE "validation_check_code" = 'CastingOutdated'`,
    )
  }
}
