import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddValidationChecksSchema1753221335718
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "validation_check" ("validation_check_id" SERIAL NOT NULL, "validation_check_code" character varying NOT NULL, CONSTRAINT "uq_valcheckcode" UNIQUE ("validation_check_code"), CONSTRAINT "validation_check_id_pk" PRIMARY KEY ("validation_check_id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "dci_response_validation_check" ("dci_response_validation_check_id" SERIAL NOT NULL, "dci_response_pattern_group_id" integer, "validation_check_id" integer, CONSTRAINT "dci_response_validation_check_id_pk" PRIMARY KEY ("dci_response_validation_check_id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "dci_response_validation_check" ADD CONSTRAINT "dcirspptrngrp_to_dcirespvalcheck_fk" FOREIGN KEY ("dci_response_pattern_group_id") REFERENCES "dci_response_pattern_group"("dci_response_pattern_group_id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "dci_response_validation_check" ADD CONSTRAINT "dcirespvalcheck_to_validationcheck_fk" FOREIGN KEY ("validation_check_id") REFERENCES "validation_check"("validation_check_id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "dci_response_validation_check" DROP CONSTRAINT "dcirespvalcheck_to_validationcheck_fk"`,
    )
    await queryRunner.query(
      `ALTER TABLE "dci_response_validation_check" DROP CONSTRAINT "dcirspptrngrp_to_dcirespvalcheck_fk"`,
    )
    await queryRunner.query(`DROP TABLE "dci_response_validation_check"`)
    await queryRunner.query(`DROP TABLE "validation_check"`)
  }
}
