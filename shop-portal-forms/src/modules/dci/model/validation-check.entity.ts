import {
  Column,
  <PERSON>tity,
  OneToMany,
  PrimaryGeneratedColumn,
  Unique,
  type Relation,
} from 'typeorm'
import { type DCIResponseValidationCheck } from './dci-response-validation-check.entity'

@Entity({
  name: 'validation_check',
})
@Unique('uq_valcheckcode', ['code'])
export class ValidationCheck {
  @PrimaryGeneratedColumn({
    name: 'validation_check_id',
    primaryKeyConstraintName: 'validation_check_id_pk',
  })
  id: number

  @Column({
    name: 'validation_check_code',
    nullable: false,
  })
  code: string

  @OneToMany(
    'DCIResponseValidationCheck',
    (check: DCIResponseValidationCheck) => check.validationCheck,
  )
  responses: Relation<DCIResponseValidationCheck[]>
}
