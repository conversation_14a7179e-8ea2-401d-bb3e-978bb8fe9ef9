import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm'
import { DCIResponsePatternGroup } from './dci-response-pattern-group.entity'
import { ValidationCheck } from './validation-check.entity'

@Entity({
  name: 'dci_response_validation_check',
})
export class DCIResponseValidationCheck {
  @PrimaryGeneratedColumn({
    name: 'dci_response_validation_check_id',
    primaryKeyConstraintName: 'dci_response_validation_check_id_pk',
  })
  id: number

  @ManyToOne(() => DCIResponsePatternGroup, (response) => response.checks, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({
    name: 'dci_response_pattern_group_id',
    foreignKeyConstraintName: 'dcirspptrngrp_to_dcirespvalcheck_fk',
  })
  response: DCIResponsePatternGroup

  @ManyToOne(() => ValidationCheck, (check) => check.responses, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({
    name: 'validation_check_id',
    foreignKeyConstraintName: 'dcirespvalcheck_to_validationcheck_fk',
  })
  validationCheck: ValidationCheck
}
