import { MigrationInterface, QueryRunner } from 'typeorm'

import definition from '../seed/data/applications/1-platform-one.json'
import { seedApplication } from 'db/seed/utils'

export class AddPlatformOneApplication1752681850284
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await seedApplication(queryRunner, definition)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DELETE FROM "applications" WHERE "application_code" = $1;`,
      [definition.application.applicationCode],
    )
  }
}
