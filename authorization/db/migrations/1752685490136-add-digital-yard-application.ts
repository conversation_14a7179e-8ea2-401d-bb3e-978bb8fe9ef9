import { MigrationInterface, QueryRunner } from 'typeorm'

import definition from '../seed/data/applications/3-platform-one-digital-yard.json'
import { seedApplication } from 'db/seed/utils'

export class AddDigitalYardApplication1752685490136
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await seedApplication(queryRunner, definition)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DELETE FROM "applications" WHERE "application_code" = $1;`,
      [definition.application.applicationCode],
    )
  }
}
