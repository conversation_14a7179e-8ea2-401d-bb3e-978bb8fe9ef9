import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddUniquenessConstraints1752681850283
  implements MigrationInterface
{
  name = 'AddUniquenessConstraints1752681850283'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "applications" ADD CONSTRAINT "UQ_e40c9f907bddda8149cc07730db" UNIQUE ("application_code")`,
    )
    await queryRunner.query(
      `ALTER TABLE "permissions" ADD CONSTRAINT "UQ_b990eff1fc3540798960d80e452" UNIQUE ("permission_name")`,
    )
    await queryRunner.query(
      `ALTER TABLE "roles" ADD CONSTRAINT "UQ_ac35f51a0f17e3e1fe121126039" UNIQUE ("role_name")`,
    )
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "UQ_063cc64e23592805d94401722aa" UNIQUE ("lan_id")`,
    )
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_role_permissions_uniqueness" ON "role_permissions" ("role_id", "permission_id") `,
    )
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_application_users_uniqueness" ON "application_users" ("application_id", "user_id") `,
    )
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_user_roles_scope_uniqueness" ON "user_roles" ("user_id", "role_id", "scope") `,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."idx_user_roles_scope_uniqueness"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."idx_application_users_uniqueness"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."idx_role_permissions_uniqueness"`,
    )
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "UQ_063cc64e23592805d94401722aa"`,
    )
    await queryRunner.query(
      `ALTER TABLE "roles" DROP CONSTRAINT "UQ_ac35f51a0f17e3e1fe121126039"`,
    )
    await queryRunner.query(
      `ALTER TABLE "permissions" DROP CONSTRAINT "UQ_b990eff1fc3540798960d80e452"`,
    )
    await queryRunner.query(
      `ALTER TABLE "applications" DROP CONSTRAINT "UQ_e40c9f907bddda8149cc07730db"`,
    )
  }
}
