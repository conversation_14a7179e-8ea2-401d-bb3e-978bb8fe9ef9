import { QueryRunner } from 'typeorm'

type ApplicationDefinition = {
  application: {
    applicationCode: string
    applicationName: string
    applicationDescription: string
    parentApplication?: string
  }
  permissions: string[]
  roles: {
    roleName: string
    roleDescription: string
    permissions: string[]
  }[]
}

export const seedApplication = async (
  queryRunner: QueryRunner,
  definition: ApplicationDefinition,
): Promise<void> => {
  const { application, permissions, roles } = definition

  await queryRunner.query(
    `INSERT INTO "applications"
      ("application_code", "application_name", "application_description", "application_uri", "parent_application_id")
      VALUES ($1, $2, $3, '', (SELECT application_id FROM applications WHERE application_code = $4))
      ON CONFLICT DO NOTHING;`,
    [
      application.applicationCode,
      application.applicationName,
      application.applicationDescription,
      application.parentApplication,
    ],
  )

  for (const permission of permissions) {
    await queryRunner.query(
      `INSERT INTO "permissions" ("permission_name") VALUES ($1) ON CONFLICT DO NOTHING;`,
      [permission],
    )
  }

  for (const role of roles) {
    await queryRunner.query(
      `INSERT INTO "roles" ("role_name", "role_description") VALUES ($1, $2) ON CONFLICT DO NOTHING;`,
      [role.roleName, role.roleDescription],
    )

    await queryRunner.query(
      `INSERT INTO "application_roles" ("application_id", "role_id", "created_date", "created_by")
        VALUES (
          (SELECT application_id FROM applications WHERE application_code = $1),
          (SELECT role_id FROM roles WHERE role_name = $2),
          NOW(),
          'SYSTEM'
        ) ON CONFLICT DO NOTHING;`,
      [application.applicationCode, role.roleName],
    )

    for (const permission of role.permissions) {
      await queryRunner.query(
        `INSERT INTO "role_permissions" ("role_id", "permission_id", "created_date", "created_by")
          VALUES (
            (SELECT role_id FROM roles WHERE role_name = $1),
            (SELECT permission_id FROM permissions WHERE permission_name = $2),
            NOW(),
            'SYSTEM'
          ) ON CONFLICT DO NOTHING;`,
        [role.roleName, permission],
      )
    }
  }
}
