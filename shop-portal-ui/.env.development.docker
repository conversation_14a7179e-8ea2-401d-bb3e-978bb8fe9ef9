SERVICE_API_ROOT_FORMS=http://host.docker.internal:3003
SERVICE_API_ROOT_RAILCARS=http://host.docker.internal:3001
SERVICE_API_ROOT_SERVICE_EVENTS=http://host.docker.internal:3002
GATX_SERVICE_API_ROOT_INTERCHANGEABLE_PARTS=http://host.docker.internal:3030/asset/interchangeableParts
GATX_SERVICE_API_ROOT_IVP_MECHANICAL_REC=http://host.docker.internal:3030/asset/ivpMechanicalRecs
GATX_SERVICE_API_ROOT_ECM=http://host.docker.internal:3030/asset/documents
GATX_SERVICE_API_ROOT_MULESOFT=http://host.docker.internal:3030/asset/documents
GATX_SERVICE_API_ROOT_PROGRAM_DEFINITIONS=http://host.docker.internal:3030/serviceEvent/

NEXT_PUBLIC_SERWIST_LOGS_ENABLED=false
