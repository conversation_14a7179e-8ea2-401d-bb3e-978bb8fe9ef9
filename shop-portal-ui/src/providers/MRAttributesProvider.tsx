import { useRailcarMRAttributes } from '@/features/railcars/hooks/useRailcarMRAttributes'
import { RailcarMRAttributes } from '@/features/railcars/types/Railcar'
import { createContext, PropsWithChildren, use, useMemo } from 'react'
import { useAppContext } from './AppProvider'

const MRAttributesContext = createContext<Record<
  string,
  RailcarMRAttributes[]
> | null>(null)

const MRAttributesProvider = ({ children }: PropsWithChildren) => {
  const { carNumber } = useAppContext()
  const { data: mrAttributes } = useRailcarMRAttributes({
    carNumber,
  })

  const attributes = useMemo(() => {
    if (!mrAttributes) {
      return null
    }

    const attrsByLocation = {} as Record<string, RailcarMRAttributes[]>

    for (const attr of mrAttributes) {
      const locationCode = attr.mrLocationCode

      if (!attrsByLocation[locationCode]) {
        attrsByLocation[locationCode] = []
      } else {
        attrsByLocation[locationCode].push(attr)
      }
    }

    return attrsByLocation
  }, [mrAttributes])

  return (
    <MRAttributesContext.Provider value={attributes}>
      {children}
    </MRAttributesContext.Provider>
  )
}

function useMRAttributesByLocation() {
  return use(MRAttributesContext)
}

export default MRAttributesProvider

export { useMRAttributesByLocation }
