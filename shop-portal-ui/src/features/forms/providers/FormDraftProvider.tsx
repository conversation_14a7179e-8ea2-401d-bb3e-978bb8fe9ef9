import { useAppContext } from '@/providers/AppProvider'
import { createContext, PropsWithChildren, use, useMemo } from 'react'
import { useFormDraft } from '../hooks/useFormDraft'

const FormDraftContext = createContext<ReturnType<typeof useFormDraft> | null>(
  null,
)

const FormDraftProvider = ({ children }: PropsWithChildren) => {
  const { formId = '' } = useAppContext()

  const [draft, setDraft] = useFormDraft(formId)

  const draftContext = useMemo(
    () => [draft, setDraft] as const,
    [draft, setDraft],
  )

  return (
    <FormDraftContext.Provider value={draftContext}>
      {children}
    </FormDraftContext.Provider>
  )
}

function useCurrentFormDraft() {
  const draftContext = use(FormDraftContext)

  if (draftContext === null) {
    throw new Error(
      'useCurrentFormDraft must be used within a FormDraftProvider',
    )
  }

  return draftContext
}

export { FormDraftProvider, useCurrentFormDraft }
