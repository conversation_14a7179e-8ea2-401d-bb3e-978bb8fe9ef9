import { useRef } from 'react'

/**
 * Makes the array reference stable.
 *
 * The returned value is guaranteed to be the exact same array (per Object.is) if
 * the contents are unchanged.
 *
 * Useful when the array is used as a dependency in useEffect, useMemo or useCallback,
 * provided the cost of computing array equality is less than the cost of running the effect or recalculating the memoized value.
 *
 * @param array - The array to stabilize.
 * @returns A stable array reference.
 */
export function useStableArray<T>(array: T[]): T[] {
  const ref = useRef(array)

  if (
    ref.current.length !== array.length ||
    ref.current.some((value, index) => value !== array[index])
  ) {
    ref.current = array
  }

  return ref.current
}
