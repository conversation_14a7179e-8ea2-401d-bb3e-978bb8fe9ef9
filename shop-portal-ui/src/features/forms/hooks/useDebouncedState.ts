import { J<PERSON> } from '@/types/Json'
import { useEffect, useRef, useState } from 'react'

/**
 * Debounces a [value, setValue] state pair.
 *
 * Meant to be used when state updates are slow to circle back to the component,
 * causing unresponsiveness or stale updates.
 *
 * Effectively, this stores a local version of the state which is updated on real time.
 * The parent state is synced with the local state after a delay.
 *
 * @param value - The parent state value.
 * @param setValue - The function to set the parent state value.
 * @returns A [value, setValue] state pair that syncs with the parent state after a delay.
 */
export function useDebouncedState<T>(
  value: Json,
  setValue: (value: Json) => void,
  parser: (v: Json) => T,
): [T, (value: T) => void] {
  const parsed = parser(value)
  const [cached, setCached] = useState<T>(parsed)

  const timeout = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (parsed === cached) {
      return
    }

    const t = setTimeout(() => {
      setValue(cached as Json)
      timeout.current = null
    }, 500)

    timeout.current = t

    return () => {
      clearTimeout(t)
      timeout.current = null
    }
  }, [parsed, cached, setValue])

  useEffect(() => {
    if (!timeout.current) {
      setCached(parsed)
    }
  }, [parsed])

  return [cached, setCached]
}
