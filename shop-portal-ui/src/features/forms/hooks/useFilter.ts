import { useSearchParams } from 'react-router-dom'
import { z } from 'zod'

export const FILTERS = ['all', 'defects', 'incomplete', 'IVPs'] as const

const Filter = z
  .enum(FILTERS)
  .nullable()
  .transform((f) => f ?? 'all')
  .catch('all')

export type Filter = z.infer<typeof Filter>

export function useFilter(): [Filter, (f: Filter) => void] {
  const [searchParams, setSearchParams] = useSearchParams()

  const filter = Filter.parse(searchParams.get('filter'))

  function setFilter(f: Filter) {
    setSearchParams((prev) => {
      if (f === 'all') {
        prev.delete('filter')
      } else {
        prev.set('filter', f)
      }

      return prev
    })
  }

  return [filter, setFilter]
}
