import { getOptions } from '@/features/forms/api/getOptions'
import { NonBlankString } from '@/features/forms/types/Field'
import { merge } from '@/features/forms/types/FormBranch'
import options from '@/features/forms/types/FormOptions'
import { z } from 'zod'
import { mergeAll } from '../../../types/FormBranch'
import { allCorrect, mr, reply } from '../../types/MRData'

const PaintSystemOptions = options().async(() =>
  getOptions({ name: 'IVPExtPaintSystem' }),
)
const PaintColorOptions = options().async(() =>
  getOptions({ name: 'IVPExtPaintColor' }),
)

const WithYearPaintedReply = reply({
  name: 'yearPainted',
  change: NonBlankString,
})
const WithPaintedByReply = reply({
  name: 'paintedBy',
  change: z.string().max(4),
})
const WithPaintSystemReply = reply({
  name: 'paintSystem',
  change: PaintSystemOptions,
})
const WithPaintColorReply = reply({
  name: 'paintColor',
  change: PaintColorOptions,
})

const IVPExteriorPaint = mr({
  withData: merge(
    z.object({
      yearPainted_MR: z.string(),
      paintedBy_MR: z.string(),
      paintSystem_MR: z.string(),
      paintColor_MR: z.string(),
    }),
    allCorrect(
      mergeAll(
        WithYearPaintedReply,
        WithPaintedByReply,
        WithPaintSystemReply,
        WithPaintColorReply,
      ),
    ),
  ),
  withoutData: {
    yearPainted: NonBlankString,
    paintedBy: NonBlankString.max(4),
    paintSystem: PaintSystemOptions,
    paintColor: PaintColorOptions,
  },
})
type IVPExteriorPaint = z.infer<typeof IVPExteriorPaint>

export { IVPExteriorPaint }
