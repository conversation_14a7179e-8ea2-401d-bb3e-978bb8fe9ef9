import FormChoiceGroupField from '@/features/forms/components/FormChoiceGroupField'
import FormDateField from '@/features/forms/components/FormDateField'
import FormTextField from '@/features/forms/components/FormTextField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { FALSE, TRUE } from '@/features/forms/types/FormSchemaConstants'
import { toCalendarDate } from '@/utils/dates'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { useEffect } from 'react'
import FormSelectField from '../../components/FormSelectField'
import { useFormQuestionResponse } from '../../providers/FormQuestionProvider'
import { IVPField } from '../components/IVPField'

const IVPExteriorPaint = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ path: [], signable: true })
  const [response, setResponse] = useFormQuestionResponse()

  const res = response as unknown as {
    yearPainted: string
  }

  useEffect(() => {
    if (!res.yearPainted && !!fields.yearPainted_MR?.value) {
      setResponse((prev) => {
        return Object.assign({}, prev, {
          yearPainted: toCalendarDate(fields.yearPainted_MR?.value as string),
        })
      }).catch(console.error)
    }
  }, [
    fields?.allCorrect?.value,
    fields?.yearPainted_MR?.value,
    res,
    setResponse,
  ])

  return (
    <fieldset>
      {fields.hasMRData?.value === FALSE && (
        <>
          <Alert
            title={t('noMRData')}
            urgency="immediate"
            level="warning"
            compact
          >
            {t('noMRDataText')}
          </Alert>

          <div className="flex flex-wrap mt-xl gap-2xl">
            {fields.yearPainted && (
              <div className="flex-1">
                <FormDateField
                  {...fields.yearPainted}
                  label={t('IVPExteriorPaint.yearPainted_MR')}
                />
              </div>
            )}

            {fields.paintedBy && (
              <div className="flex-1">
                <FormTextField
                  {...fields.paintedBy}
                  label={t('IVPExteriorPaint.paintedBy_MR')}
                />
              </div>
            )}

            {fields.paintSystem && (
              <div className="flex-1">
                <FormSelectField
                  {...fields.paintSystem}
                  label={t('IVPExteriorPaint.paintSystem_MR')}
                />
              </div>
            )}

            {fields.paintColor && (
              <div className="flex-1">
                <FormSelectField
                  {...fields.paintColor}
                  label={t('IVPExteriorPaint.paintColor_MR')}
                />
              </div>
            )}
          </div>
        </>
      )}

      {fields.hasMRData?.value === TRUE && (
        <>
          {fields.allCorrect && (
            <FormChoiceGroupField
              size="large"
              {...fields.allCorrect}
              label={t('common.allCorrect')}
            />
          )}

          <div className="inline-grid grid-cols-[auto_auto_1fr] gap-2xl mt-xl items-start">
            <IVPField
              id="yearPainted"
              CorrectionComponent={FormDateField}
              fields={fields}
            />
            <IVPField
              id="paintedBy"
              CorrectionComponent={FormTextField}
              fields={fields}
            />
            <IVPField
              id="paintSystem"
              CorrectionComponent={FormSelectField}
              fields={fields}
            />
            <IVPField
              id="paintColor"
              CorrectionComponent={FormSelectField}
              fields={fields}
            />
          </div>
        </>
      )}
    </fieldset>
  )
}

export default IVPExteriorPaint
