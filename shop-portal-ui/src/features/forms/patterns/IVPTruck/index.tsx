import FormChoiceGroup<PERSON>ield from '@/features/forms/components/FormChoiceGroupField'
import FormSelectField from '@/features/forms/components/FormSelectField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { FALSE, TRUE } from '../../types/FormSchemaConstants'
import { IVPField } from '../components/IVPField'

const IVPTruck = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ path: [], signable: true })

  return (
    <fieldset>
      {fields.hasMRData?.value === FALSE && (
        <>
          <Alert
            title={t('noMRData')}
            urgency="immediate"
            level="warning"
            compact
          >
            {t('noMRDataText')}
          </Alert>

          <div className="flex flex-wrap mt-xl gap-2xl">
            {fields.truckType && (
              <div className="flex-1">
                <FormSelectField
                  {...fields.truckType}
                  label={t('IVPTruck.truckType_MR')}
                />
              </div>
            )}

            {fields.brakeSystem && (
              <div className="flex-1">
                <FormSelectField
                  {...fields.brakeSystem}
                  label={t('IVPTruck.brakeSystem_MR')}
                />
              </div>
            )}

            {fields.springGrouping && (
              <div className="flex-1">
                <FormSelectField
                  {...fields.springGrouping}
                  label={t('IVPTruck.springGrouping_MR')}
                />
              </div>
            )}
          </div>
        </>
      )}

      {fields.hasMRData?.value === TRUE && (
        <>
          {fields.allCorrect && (
            <FormChoiceGroupField
              size="large"
              {...fields.allCorrect}
              label={t('common.allCorrect')}
            />
          )}

          <div className="inline-grid grid-cols-[auto_auto_1fr] gap-2xl mt-xl items-start">
            <IVPField
              id="truckType"
              CorrectionComponent={FormSelectField}
              fields={fields}
            />
            <IVPField
              id="brakeSystem"
              CorrectionComponent={FormSelectField}
              fields={fields}
            />
            <IVPField
              id="springGrouping"
              CorrectionComponent={FormSelectField}
              fields={fields}
            />
          </div>
        </>
      )}
    </fieldset>
  )
}

export default IVPTruck
