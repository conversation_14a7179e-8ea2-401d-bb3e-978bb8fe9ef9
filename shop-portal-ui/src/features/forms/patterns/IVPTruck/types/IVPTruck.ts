import { getOptions } from '@/features/forms/api/getOptions'
import { merge } from '@/features/forms/types/FormBranch'
import { z } from 'zod'
import { mergeAll } from '../../../types/FormBranch'
import options from '../../../types/FormOptions'
import { allCorrect, mr, reply } from '../../types/MRData'

const TruckTypeOptions = options().async((query) =>
  getOptions({ name: 'IVPTruckType', ...query }),
)
const BrakeSystemOptions = options().async((query) =>
  getOptions({ name: 'IVPBrakeType', ...query }),
)
const SpringGroupingOptions = options().async((query) =>
  getOptions({ name: 'IVPTrkSpringGrouping', ...query }),
)

const WithTruckTypeReply = reply({
  name: 'truckType',
  change: TruckTypeOptions,
})
const WithBrakeSystemReply = reply({
  name: 'brakeSystem',
  change: BrakeSystemOptions,
})
const WithSpringGroupingReply = reply({
  name: 'springGrouping',
  change: SpringGroupingOptions,
})

const IVPTruck = mr({
  withData: merge(
    z.object({
      truckType_MR: TruckTypeOptions,
      brakeSystem_MR: BrakeSystemOptions,
      springGrouping_MR: SpringGroupingOptions,
    }),
    allCorrect(
      mergeAll(
        WithTruckTypeReply,
        WithBrakeSystemReply,
        WithSpringGroupingReply,
      ),
    ),
  ),
  withoutData: {
    truckType: TruckTypeOptions,
    brakeSystem: BrakeSystemOptions,
    springGrouping: SpringGroupingOptions,
  },
})
type IVPTruck = z.infer<typeof IVPTruck>

export { IVPTruck }
