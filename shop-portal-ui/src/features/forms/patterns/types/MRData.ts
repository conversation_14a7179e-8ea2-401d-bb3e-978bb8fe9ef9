import { z, <PERSON><PERSON><PERSON><PERSON><PERSON>ha<PERSON>, ZodTypeAny } from 'zod'
import branch, { FormBranch, merge } from '../../types/FormBranch'
import {
  CantTell,
  Change,
  False,
  No,
  OK,
  True,
  Yes,
} from '../../types/FormOptions'
import { HAS_MR_DATA } from '../../types/FormSchemaConstants'

const WithMRData = z.object({
  [HAS_MR_DATA]: True,
  // FIXME: this may not apply to every pattern using this utility.
  currentData_MR: z.string().optional(),
})

const WithNoMRData = z.object({
  [HAS_MR_DATA]: False,
})

function mr<
  O extends readonly [ZodTypeAny, ZodTypeAny, ...ZodTypeAny[]],
  S extends ZodRawShape,
>({ withData, withoutData }: { withData: FormBranch<O>; withoutData: S }) {
  return branch(
    // @ts-expect-error TODO: enhance type inference.
    [HAS_MR_DATA],
    merge(WithMRData, withData),
    WithNoMRData.extend(withoutData),
  )
}

const reply = ({
  name,
  change,
  withOk = true,
  suffix = '_reply',
}: {
  name: string
  change: z.ZodSchema
  withOk?: boolean
  suffix?: string
}) =>
  branch(
    [`${name}${suffix}`],
    z.object({
      [`${name}${suffix}`]: withOk ? OK.$or(CantTell) : CantTell,
    }),
    z.object({
      [`${name}${suffix}`]: Change,
      [name]: change,
    }),
  )
const allCorrect = <
  O extends readonly [ZodTypeAny, ZodTypeAny, ...ZodTypeAny[]],
>(
  formBranch: FormBranch<O>,
) =>
  branch(
    ['allCorrect'],
    z.object({
      allCorrect: Yes,
    }),
    merge(
      z.object({
        allCorrect: No,
      }),
      formBranch,
    ),
  )

export { allCorrect, mr, reply, WithMRData, WithNoMRData }
