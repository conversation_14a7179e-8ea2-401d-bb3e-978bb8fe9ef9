import { merge, mergeAll } from '@/features/forms/types/FormBranch'
import options, { Option } from '@/features/forms/types/FormOptions'
import { z } from 'zod'
import { allCorrect, mr, reply } from '../../types/MRData'

const CastingMonthOptions = options(
  '01',
  '02',
  '03',
  '04',
  '05',
  '06',
  '07',
  '08',
  '09',
  '10',
  '11',
  '12',
)

const getCastingYearOptions = () => {
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 76 }, (_, i) => {
    const year = currentYear - i
    return {
      label: year.toString(),
      value: (year % 100).toString().padStart(2, '0'),
    } as Option
  })

  return years
}

const CastingYearOptions = options(
  ...(getCastingYearOptions() as [Option, ...Option[]]),
)

const CastingMonth = reply({
  name: 'castingMonth',
  change: CastingMonthOptions,
})
const CastingYear = reply({ name: 'castingYear', change: CastingYearOptions })

const IVPCastingDate = mr({
  withData: merge(
    z.object({
      castingMonth_MR: z.string().optional(),
      castingYear_MR: z.string().optional(),
      castingDate_MR: z.string().optional(),
    }),
    allCorrect(mergeAll(CastingMonth, CastingYear)),
  ),
  withoutData: {
    castingMonth: CastingMonthOptions,
    castingYear: CastingYearOptions,
  },
})

type IVPCastingDate = z.infer<typeof IVPCastingDate>

export { IVPCastingDate }
