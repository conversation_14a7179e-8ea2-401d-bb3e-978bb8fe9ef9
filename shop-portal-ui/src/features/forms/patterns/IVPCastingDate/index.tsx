import FormChoiceGroup<PERSON>ield from '@/features/forms/components/FormChoiceGroupField'
import FormTextField from '@/features/forms/components/FormTextField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import clsx from 'clsx'
import { useEffect } from 'react'
import FormSelectField from '../../components/FormSelectField'
import { useFormQuestionResponse } from '../../providers/FormQuestionProvider'
import { FALSE, NO, TRUE } from '../../types/FormSchemaConstants'

const IVPCastingDate = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ signable: true })

  const [response, setResponse] = useFormQuestionResponse()
  const getCastingMonth = (castingDate: string) => {
    return castingDate.slice(4, 6)
  }

  const getCastingYearLabel = (castingDate: string) => {
    return castingDate.slice(0, 4)
  }

  const getCastingYearValue = (castingDate: string) => {
    return castingDate.slice(2, 4)
  }

  const res = response as unknown as {
    castingMonth: string
    castingYear: string
  }

  useEffect(() => {
    if (
      !res.castingMonth &&
      !res.castingYear &&
      !!fields?.allCorrect?.value &&
      !!fields.castingDate_MR?.value
    ) {
      const castingMonth = getCastingMonth(
        fields.castingDate_MR.value as string,
      )
      const castingYear = getCastingYearValue(
        fields.castingDate_MR.value as string,
      )

      setResponse((prev) => {
        return Object.assign({}, prev, {
          castingMonth,
          castingYear,
        })
      }).catch(console.error)
    }
  }, [
    fields?.allCorrect?.value,
    fields?.castingDate_MR?.value,
    res,
    setResponse,
  ])

  return (
    <fieldset>
      {fields.hasMRData?.value === FALSE && (
        <>
          <Alert
            title={t('noMRData')}
            urgency="immediate"
            level="warning"
            compact
          >
            {t('noMRDataText')}
          </Alert>

          <div className="flex mt-xl gap-xl">
            {fields.castingMonth && (
              <FormSelectField
                {...fields.castingMonth}
                label={t('IVPCastingDate.castingMonth_MR')}
              />
            )}

            {fields.castingYear && (
              <FormSelectField
                {...fields.castingYear}
                label={t('IVPCastingDate.castingYear_MR')}
              />
            )}
          </div>
        </>
      )}

      {fields.hasMRData?.value === TRUE && (
        <>
          {fields.allCorrect && (
            <FormChoiceGroupField
              size="large"
              {...fields.allCorrect}
              label={t('common.allCorrect')}
            />
          )}

          <div className="inline-grid grid-cols-[auto_auto_1fr] gap-2xl mt-xl items-start">
            <div className="contents">
              <div
                className={clsx(
                  (fields.allCorrect?.value !== NO ||
                    fields.castingMonth_reply?.hidden) &&
                    'col-span-3',
                )}
              >
                <FormTextField
                  readOnly
                  value={
                    fields.castingDate_MR?.value
                      ? getCastingMonth(fields.castingDate_MR.value as string)
                      : t('none')
                  }
                  label={t('IVPCastingDate.castingMonth_MR')}
                  onChange={() => undefined}
                />
              </div>
              {fields.allCorrect?.value === NO && fields.castingMonth_reply && (
                <div
                  className={clsx(
                    (!fields.castingMonth_reply?.value ||
                      fields.castingMonth?.hidden) &&
                      'col-span-2',
                  )}
                >
                  <FormChoiceGroupField
                    size="large"
                    {...fields.castingMonth_reply}
                    label={t('common.response')}
                  />
                </div>
              )}

              {fields.castingMonth_reply?.value && fields.castingMonth && (
                <FormSelectField
                  {...fields.castingMonth}
                  label={t('common.correction')}
                />
              )}
            </div>

            <div className="contents">
              <div
                className={clsx(
                  (fields.allCorrect?.value !== NO ||
                    fields.castingDate_reply?.hidden) &&
                    'col-span-3',
                )}
              >
                <FormTextField
                  readOnly
                  value={
                    fields.castingDate_MR?.value
                      ? getCastingYearLabel(
                          fields.castingDate_MR.value as string,
                        )
                      : t('none')
                  }
                  label={t('IVPCastingDate.castingYear_MR')}
                  onChange={() => undefined}
                />
              </div>

              {fields.allCorrect?.value === NO && fields.castingYear_reply && (
                <div
                  className={clsx(
                    (!fields.castingYear_reply?.value ||
                      fields.castingYear?.hidden) &&
                      'col-span-2',
                  )}
                >
                  <FormChoiceGroupField
                    size="large"
                    {...fields.castingYear_reply}
                    label={t('common.response')}
                  />
                </div>
              )}

              {fields.castingYear_reply?.value && fields.castingYear && (
                <FormSelectField
                  {...fields.castingYear}
                  label={t('common.correction')}
                />
              )}
            </div>
          </div>
        </>
      )}
    </fieldset>
  )
}

export { IVPCastingDate }
