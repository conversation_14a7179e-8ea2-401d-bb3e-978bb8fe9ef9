import FormTextField from '@/features/forms/components/FormTextField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import FormChoiceGroupField from '../../components/FormChoiceGroupField'
import { FALSE, TRUE } from '../../types/FormSchemaConstants'
import { IVPField } from '../components/IVPField'

const IVPCommonEquippedText = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ path: [], signable: true })

  return (
    <fieldset>
      {fields.hasMRData?.value === FALSE && (
        <>
          <Alert
            title={t('noMRData')}
            urgency="immediate"
            level="warning"
            compact
          >
            {t('noMRDataText')}
          </Alert>

          <div className="flex mt-xl">
            {fields.currentData && (
              <FormTextField
                {...fields.currentData}
                label={t('IVPCommonEquippedText.currentData_MR')}
                placeholder={t(
                  'IVPCommonEquippedText.currentData_MR.placeholder',
                )}
              />
            )}
          </div>
        </>
      )}

      {fields.hasMRData?.value === TRUE && (
        <>
          {fields.allCorrect && (
            <FormChoiceGroupField
              size="large"
              {...fields.allCorrect}
              label={t('common.allCorrect')}
            />
          )}
          <div className="inline-grid grid-cols-[auto_auto_1fr] gap-2xl mt-xl items-start">
            <IVPField
              id="currentData"
              CorrectionComponent={FormTextField}
              fields={fields}
            />
          </div>
        </>
      )}
    </fieldset>
  )
}

export default IVPCommonEquippedText
