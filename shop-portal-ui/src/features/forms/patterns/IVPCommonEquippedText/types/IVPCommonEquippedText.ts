import { allCorrect, mr, reply } from '@/features/forms/patterns/types/MRData'
import { NonBlankString } from '@/features/forms/types/Field'
import { merge } from '@/features/forms/types/FormBranch'
import { z } from 'zod'

const WithCurrentData = reply({
  name: 'currentData',
  change: NonBlankString,
  withOk: false,
})

const IVPCommonEquippedText = mr({
  withData: merge(
    z.object({
      currentData_MR: z.string(),
    }),
    allCorrect(WithCurrentData),
  ),
  withoutData: {
    currentData: NonBlankString,
  },
})

type IVPCommonEquippedText = z.infer<typeof IVPCommonEquippedText>

export { IVPCommonEquippedText }
