import FormTextField from '@/features/forms/components/FormTextField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'

const IVPCommonNotEquipped = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ path: [], signable: true })

  return (
    <fieldset className="space-y-xl">
      <div className="blank-print:hidden">
        <div className="contents print:hidden">
          <Alert
            title={t('IVPCommonNotEquipped.title')}
            urgency="inline"
            level="info"
            compact
            className="print:hidden"
          >
            {t('IVPCommonNotEquipped.itemWillBeRemoved')}
          </Alert>
        </div>
        <div className="hidden print:block">
          {t('IVPCommonNotEquipped.title') +
            ' — ' +
            t('IVPCommonNotEquipped.itemWillBeRemoved')}
        </div>
      </div>

      <FormTextField
        readOnly
        value={
          fields.currentData_MR?.value
            ? (fields.currentData_MR.value as string)
            : t('none')
        }
        label={t('IVPCommonNotEquipped.currentData_MR')}
      />
    </fieldset>
  )
}

export default IVPCommonNotEquipped
