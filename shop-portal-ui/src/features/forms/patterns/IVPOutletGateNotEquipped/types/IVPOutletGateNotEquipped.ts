import branch, { merge } from '@/features/forms/types/FormBranch'
import { HAS_MR_DATA } from '@/features/forms/types/FormSchemaConstants'
import { z } from 'zod'
import { allCorrect, mr, WithNoMRData } from '../../types/MRData'

const WithCurrentData = z
  .object({
    manufacturerMr_disp: z.string().optional(),
    modelMr_disp: z.string().optional(),
    typeMr_disp: z.string().optional(),
    materialMr_disp: z.string().optional(),
    connectionTypeMr_disp: z.string().optional(),
    locationMr_disp: z.string().optional(),
  })
  .array()


type IVPOutletGateNotEquipped = z.infer<typeof IVPOutletGateNotEquipped>

export { IVPOutletGateNotEquipped }
