import FormChoiceGroup<PERSON>ield from '@/features/forms/components/FormChoiceGroupField'
import FormDateField from '@/features/forms/components/FormDateField'
import FormTextField from '@/features/forms/components/FormTextField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { FALSE, TRUE } from '@/features/forms/types/FormSchemaConstants'
import { toCalendarDate } from '@/utils/dates'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { useEffect } from 'react'
import { useFormQuestionResponse } from '../../providers/FormQuestionProvider'
import { IVPField } from '../components/IVPField'

const IVPLightweight = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ path: [], signable: true })
  const [response, setResponse] = useFormQuestionResponse()

  const res = response as unknown as {
    lightWeightDate: string
  }

  useEffect(() => {
    if (!res.lightWeightDate && !!fields.lightWeightDate_MR?.value) {
      setResponse((prev) => {
        return Object.assign({}, prev, {
          lightWeightDate: toCalendarDate(
            fields.lightWeightDate_MR?.value as string,
          ),
        })
      }).catch(console.error)
    }
  }, [
    fields?.allCorrect?.value,
    fields?.lightWeightDate_MR?.value,
    res,
    setResponse,
  ])

  return (
    <fieldset>
      {fields.hasMRData?.value === FALSE && (
        <>
          <Alert
            title={t('noMRData')}
            urgency="immediate"
            level="warning"
            compact
          >
            {t('noMRDataText')}
          </Alert>

          <div className="flex flex-wrap mt-xl gap-2xl">
            {fields.lightWeight && (
              <div className="flex-1">
                <FormTextField
                  {...fields.lightWeight}
                  label={t('IVPLightweight.lightWeight_MR')}
                />
              </div>
            )}

            {fields.lightWeightDate && (
              <div className="flex-1">
                <FormDateField
                  {...fields.lightWeightDate}
                  label={t('IVPLightweight.lightWeightDate_MR')}
                />
              </div>
            )}

            {fields.lightWeightBy && (
              <div className="flex-1">
                <FormTextField
                  {...fields.lightWeightBy}
                  label={t('IVPLightweight.lightWeightBy_MR')}
                />
              </div>
            )}
          </div>
        </>
      )}

      {fields.hasMRData?.value === TRUE && (
        <>
          {fields.allCorrect && (
            <FormChoiceGroupField
              size="large"
              {...fields.allCorrect}
              label={t('common.allCorrect')}
            />
          )}

          <div className="inline-grid grid-cols-[auto_auto_1fr] gap-2xl mt-xl items-start">
            <IVPField
              id="lightWeight"
              CorrectionComponent={FormTextField}
              fields={fields}
            />
            <IVPField
              id="lightWeightDate"
              CorrectionComponent={FormDateField}
              fields={fields}
            />
            <IVPField
              id="lightWeightBy"
              CorrectionComponent={FormTextField}
              fields={fields}
            />
          </div>
        </>
      )}
    </fieldset>
  )
}

export default IVPLightweight
