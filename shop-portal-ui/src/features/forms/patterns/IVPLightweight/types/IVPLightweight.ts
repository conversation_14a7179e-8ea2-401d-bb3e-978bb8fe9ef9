import {
  AlphanumericString,
  NonBlankString,
} from '@/features/forms/types/Field'
import { merge } from '@/features/forms/types/FormBranch'
import { z } from 'zod'
import { mergeAll } from '../../../types/FormBranch'
import { allCorrect, mr, reply } from '../../types/MRData'

const WithLightWeightReply = reply({
  name: 'lightWeight',
  change: NonBlankString,
})
const WithLightWeightDateReply = reply({
  name: 'lightWeightDate',
  change: NonBlankString,
})
const WithLightWeightByReply = reply({
  name: 'lightWeightBy',
  change: AlphanumericString.max(4),
})

const IVPLightweight = mr({
  withData: merge(
    z.object({
      lightWeight_MR: z.string(),
      lightWeightDate_MR: z.string(),
      lightWeightBy_MR: z.string(),
    }),
    allCorrect(
      mergeAll(
        WithLightWeightByReply,
        WithLightWeightDateReply,
        WithLightWeightReply,
      ),
    ),
  ),
  withoutData: {
    lightWeight: NonBlankString,
    lightWeightDate: z.string(),
    lightWeightBy: AlphanumericString.max(4),
  },
})
type IVPLightweight = z.infer<typeof IVPLightweight>

export { IVPLightweight }
