import FormSelectField from '../../components/FormSelectField'
import FormTextField from '../../components/FormTextField'
import { useFormFields } from '../../hooks/useFormFields'

const WhyMade = () => {
  const fields = useFormFields({ signable: true })

  return (
    <div>
      <FormSelectField {...fields.whyMade!} printWidth="full" />
      <div className="mt-md">
        <FormTextField
          {...fields.comments!}
          printWidth="full"
          printBorder={false}
        />
      </div>
    </div>
  )
}

export default WhyMade
