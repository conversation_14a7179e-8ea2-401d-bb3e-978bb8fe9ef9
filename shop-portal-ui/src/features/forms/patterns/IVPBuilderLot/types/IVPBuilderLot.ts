import {
  NonBlankString,
  RequiredStringWithMaxLength,
} from '@/features/forms/types/Field'
import { merge, mergeAll } from '@/features/forms/types/FormBranch'
import { z } from 'zod'
import { allCorrect, mr, reply } from '../../types/MRData'

const WithBuilderNameReply = reply({
  name: 'builderName',
  change: RequiredStringWithMaxLength(5),
})

const WithLotNumberReply = reply({ name: 'lotNumber', change: NonBlankString })

const IVPBuilderLot = mr({
  withData: merge(
    z.object({
      builderName_MR: z.string().optional(),
      lotNumber_MR: z.string().optional(),
    }),
    allCorrect(mergeAll(WithBuilderNameReply, WithLotNumberReply)),
  ),
  withoutData: {
    builderName: RequiredStringWithMaxLength(5),
    lotNumber: NonBlankString,
  },
})

type IVPBuilderLot = z.infer<typeof IVPBuilderLot>

export { IVPBuilderLot }
