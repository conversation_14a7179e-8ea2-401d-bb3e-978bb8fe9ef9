import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import FormChoiceGroupField from '../../components/FormChoiceGroupField'
import FormSelectField from '../../components/FormSelectField'
import { FALSE, TRUE } from '../../types/FormSchemaConstants'
import { IVPField } from '../components/IVPField'

const IVPAirPiping = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ path: [], signable: true })

  return (
    <fieldset className="space-y-xl">
      {fields.hasMRData?.value === FALSE && (
        <>
          <div className="contents print:hidden">
            <Alert
              title={t('noMRData')}
              urgency="immediate"
              level="warning"
              compact
              className="print:hidden"
            >
              {t('noMRDataText')}
            </Alert>
          </div>
          <div className="hidden print:block">
            {t('noMRData') + ' — ' + t('noMRDataText')}
          </div>

          <ul className="flex flex-col md:flex-row gap-md blank-print:hidden mt-lg">
            <li className="flex-1">
              {fields.configuration && (
                <FormSelectField
                  {...fields.configuration}
                  label={t('IVPAirPiping.configuration_MR')}
                />
              )}
            </li>

            <li className="flex-1">
              {fields.outletValve && (
                <FormSelectField
                  {...fields.outletValve}
                  label={t('IVPAirPiping.outletValve_MR')}
                />
              )}
            </li>

            <li className="flex-1">
              {fields.material && (
                <FormSelectField
                  {...fields.material}
                  label={t('IVPAirPiping.material_MR')}
                />
              )}
            </li>

            <li className="flex-1">
              {fields.numberOfGates && (
                <FormSelectField
                  {...fields.numberOfGates}
                  label={t('IVPAirPiping.numberOfGates_MR')}
                />
              )}
            </li>
          </ul>
        </>
      )}

      {fields.hasMRData?.value === TRUE && (
        <>
          {fields.allCorrect && (
            <FormChoiceGroupField
              size="large"
              {...fields.allCorrect}
              label={t('common.allCorrect')}
            />
          )}
          <div className="inline-grid grid-cols-[auto_auto_1fr] gap-2xl mt-xl items-start">
            <IVPField
              id="configuration"
              CorrectionComponent={FormSelectField}
              fields={fields}
            />
            <IVPField
              id="outletValve"
              CorrectionComponent={FormSelectField}
              fields={fields}
            />
            <IVPField
              id="material"
              CorrectionComponent={FormSelectField}
              fields={fields}
            />
            <IVPField
              id="numberOfGates"
              CorrectionComponent={FormSelectField}
              fields={fields}
            />
          </div>
        </>
      )}
    </fieldset>
  )
}

export default IVPAirPiping
