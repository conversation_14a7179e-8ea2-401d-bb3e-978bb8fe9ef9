import { getOptions } from '@/features/forms/api/getOptions'
import { allCorrect, mr, reply } from '@/features/forms/patterns/types/MRData'
import { merge, mergeAll } from '@/features/forms/types/FormBranch'
import options from '@/features/forms/types/FormOptions'
import { z } from 'zod'

const Configuration = options().async(() =>
  getOptions({ name: 'IVPAirPipingConfig' }),
)

const OutletValve = options().async(() =>
  getOptions({ name: 'IVPAirPipingValve' }),
)

const Material = options().async(() =>
  getOptions({ name: 'IVPAirPipingMater' }),
)

const NumberOfGates = options().async(() =>
  getOptions({ name: 'IVPAirPipingNumOut' }),
)

const WithConfiguration = reply({
  name: 'configuration',
  change: Configuration,
})

const WithOutletValve = reply({
  name: 'outletValve',
  change: OutletValve,
})

const WithMaterial = reply({
  name: 'material',
  change: Material,
})

const WithNumberofGates = reply({
  name: 'numberOfGates',
  change: NumberOfGates,
})

const IVPAirPiping = mr({
  withData: merge(
    z.object({
      configuration_MR: z.string().optional(),
      outletValve_MR: z.string().optional(),
      material_MR: z.string().optional(),
      numberOfGates_MR: z.string().optional(),
    }),
    allCorrect(
      mergeAll(
        WithConfiguration,
        WithOutletValve,
        WithMaterial,
        WithNumberofGates,
      ),
    ),
  ),
  withoutData: {
    configuration: Configuration,
    outletValve: OutletValve,
    material: Material,
    numberOfGates: NumberOfGates,
  },
})

type IVPAirPiping = z.infer<typeof IVPAirPiping>

export { IVPAirPiping }
