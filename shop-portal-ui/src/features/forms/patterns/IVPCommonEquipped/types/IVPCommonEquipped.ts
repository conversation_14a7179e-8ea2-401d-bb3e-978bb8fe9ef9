import { getOptions } from '@/features/forms/api/getOptions'
import { merge } from '@/features/forms/types/FormBranch'
import options from '@/features/forms/types/FormOptions'
import { z } from 'zod'
import { allCorrect, mr, reply } from '../../types/MRData'

const CurrentDataOptions = options().async(
  async (query) => await getOptions(query),
)

const WithCurrentData = reply({
  name: 'currentData',
  change: CurrentDataOptions,
  withOk: false,
})

const IVPCommonEquipped = mr({
  withData: merge(
    z.object({
      currentData_MR: z.string(),
    }),
    allCorrect(WithCurrentData),
  ),
  withoutData: {
    currentData: CurrentDataOptions,
  },
})

type IVPCommonEquipped = z.infer<typeof IVPCommonEquipped>

export { IVPCommonEquipped }
