import { <PERSON><PERSON> } from '@/types/Json'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Al<PERSON> } from '@gatx-corp/platform-one-common/components/Alert'
import { Button } from '@gatx-corp/platform-one-common/components/Button'
import clsx from 'clsx'
import { memo } from 'react'
import FormChoiceGroupField from '../../components/FormChoiceGroupField'
import FormSelectField from '../../components/FormSelectField'
import PatternWithRows, {
  PatternRowProps,
} from '../../components/patterns/PatternWithRows'
import { FieldProps, useFormFields } from '../../hooks/useFormFields'
import {
  useFormQuestion,
  useFormQuestionMRData,
  useFormQuestionResponse,
  useResponseRows,
} from '../../providers/FormQuestionProvider'
import { Option } from '../../types/FormOptions'
import { FALSE, HAS_MR_DATA } from '../../types/FormSchemaConstants'
import { IVPField } from '../components/IVPField'

type IVPHatchesFields = Partial<
  Record<string, FieldProps & { disabled?: boolean }>
>

/**
 * FIXME: option ids should not be exposed to this codebase. Values should be used instead.
 */
function getOptionId(fields: IVPHatchesFields, key: string) {
  if (!fields || !fields[key] || !fields[key].options) {
    return null
  }

  const { options, value } = fields[key]

  const opt = options.find((o) => o.value === value)

  if (!opt) {
    return null
  }

  return opt.metadata.id as number
}

function getOptions(fields: IVPHatchesFields, key: string) {
  return fields?.[key]?.options ?? []
}

function by(option: Option, key: string, value: Json) {
  const metadata = option.metadata.metadata as Record<string, unknown>

  return (
    metadata &&
    typeof metadata == 'object' &&
    key in metadata &&
    metadata[key] === value
  )
}

function filterByManufacturer(options: Option[], mfactId: Json) {
  return options.filter((o) => by(o, 'manufacturerId', mfactId))
}

function filterByModel(options: Option[], modelId: Json) {
  return options.filter((o) => by(o, 'modelId', modelId))
}

function useIVPHatchesFields(index: number): IVPHatchesFields {
  const question = useFormQuestion()
  const [, setResponse] = useFormQuestionResponse()
  const fields: IVPHatchesFields = useFormFields({ path: [index.toString()] })

  const mfactId = getOptionId(fields, 'manufacturer')
  const modelId = getOptionId(fields, 'model')
  const shapeId = getOptionId(fields, 'shape')

  const models = getOptions(fields, 'model')
  const shapes = getOptions(fields, 'shape')
  const sizes = getOptions(fields, 'nominalSize')
  const styles = getOptions(fields, 'style')
  const materials = getOptions(fields, 'material')

  fields.model!.options = filterByManufacturer(models, mfactId)
  fields.shape!.options = filterByModel(shapes, modelId)
  fields.nominalSize!.options = filterByModel(sizes, modelId)
  fields.style!.options = filterByModel(styles, modelId)
  fields.material!.options = filterByModel(materials, modelId)

  fields.location!.options = getOptions(fields, 'location').filter((o) =>
    by(o, 'partTypeCode', question.partTypeCode),
  )

  fields.model!.disabled = !mfactId
  fields.shape!.disabled = !modelId
  fields.nominalSize!.disabled = !shapeId
  fields.style!.disabled = !modelId
  fields.material!.disabled = !modelId

  function nextFromModelChange(value: Json, prev: Json) {
    const opt = models.find((o) => o.value === value)!
    const modelId = opt.metadata.id as number

    const nextShapes = filterByModel(shapes, modelId)
    const nextSizes = filterByModel(sizes, modelId)
    const nextStyles = filterByModel(styles, modelId)
    const nextMaterials = filterByModel(materials, modelId)

    return Object.assign({}, prev, {
      manufacturer: fields.manufacturer!.value!,
      model: value,
      shape: nextShapes.length === 1 ? nextShapes[0].value : undefined,
      nominalSize: nextSizes.length === 1 ? nextSizes[0].value : undefined,
      style: nextStyles.length === 1 ? nextStyles[0].value : undefined,
      material: nextMaterials.length === 1 ? nextMaterials[0].value : undefined,
      shapeReply: undefined,
      nominalSizeReply: undefined,
      styleReply: undefined,
      materialReply: undefined,
    })
  }

  fields.manufacturer!.onChange = (value) => {
    const opt = fields.manufacturer!.options!.find((o) => o.value === value)!

    const mfactId = opt.metadata.id as number

    const nextModels = filterByManufacturer(models, mfactId)

    setResponse((prev) => {
      const next = [prev].flat()

      if (nextModels.length === 1) {
        next[index] = Object.assign(
          {},
          nextFromModelChange(nextModels[0].value, next[index]),
          {
            manufacturer: value,
            modelReply: undefined,
            shapeReply: undefined,
            nominalSizeReply: undefined,
            styleReply: undefined,
            materialReply: undefined,
          },
        )
      } else {
        next[index] = Object.assign({}, next[index], {
          manufacturer: value,
          model: undefined,
          modelReply: undefined,
          shape: undefined,
          shapeReply: undefined,
          nominalSize: undefined,
          nominalSizeReply: undefined,
          style: undefined,
          styleReply: undefined,
          materialReply: undefined,
        })
      }

      return next
    }).catch(console.error)
  }

  fields.model!.onChange = (value) => {
    setResponse((prev) => {
      const next = [prev].flat()

      next[index] = nextFromModelChange(value, next[index])

      return next
    }).catch(console.error)
  }

  function isHidden(dependsOn: FieldProps, value: Json) {
    return !dependsOn.value || (dependsOn.value === 'Change' && !value)
  }

  if (fields.manufacturerReply && fields.modelReply) {
    fields.modelReply.hidden = isHidden(fields.manufacturerReply, mfactId)
    fields.shapeReply!.hidden = isHidden(fields.modelReply, modelId)
    fields.nominalSizeReply!.hidden = isHidden(fields.modelReply, modelId)
    fields.styleReply!.hidden = isHidden(fields.modelReply, modelId)
    fields.materialReply!.hidden = isHidden(fields.modelReply, modelId)
  }

  return fields
}

const IVPHatchesNoDataRow = ({ index }: PatternRowProps) => {
  const fields = useIVPHatchesFields(index)

  return (
    <div
      className={clsx(
        'grid gap-md print:gap-xs',
        'grid-cols-[repeat(2,minmax(180px,1fr))] print:grid-cols-[repeat(4,1fr)]',
        '@[700px]:grid-cols-[repeat(3,minmax(180px,1fr))]',
        '@[800px]:grid-cols-[repeat(4,minmax(180px,1fr))]',
      )}
    >
      <FormSelectField noSideEffects {...fields.manufacturer!} />
      <FormSelectField noSideEffects {...fields.model!} />
      <FormSelectField noSideEffects {...fields.shape!} />
      <FormSelectField noSideEffects {...fields.nominalSize!} />
      <FormSelectField noSideEffects {...fields.style!} />
      <FormSelectField noSideEffects {...fields.material!} />
      <FormSelectField noSideEffects {...fields.location!} />
    </div>
  )
}

const IVPHatchesWithDataRow = memo(function IVPHatchesWithDataRow({
  index,
}: PatternRowProps) {
  const fields = useIVPHatchesFields(index)

  return (
    <div className="flex flex-col gap-xl items-start">
      <FormChoiceGroupField size="large" {...fields.allCorrect!} />

      <div className="grid grid-cols-[auto_auto_1fr] gap-lg items-start">
        {[
          'manufacturer',
          'model',
          'shape',
          'nominalSize',
          'style',
          'material',
          'location',
        ].map((key) => (
          <IVPField
            key={key}
            id={key}
            mrSuffix="Mr_disp"
            replySuffix="Reply"
            CorrectionComponent={FormSelectField}
            noSideEffects
            fields={fields}
          />
        ))}
      </div>
    </div>
  )
})

const IVPHatchesRow = memo(function IVPHatchesRow({ index }: PatternRowProps) {
  const data = useFormQuestionMRData()

  return (
    <div className="@container">
      {Array.isArray(data) && index >= data.length ? (
        <IVPHatchesNoDataRow index={index} />
      ) : (
        <IVPHatchesWithDataRow index={index} />
      )}
    </div>
  )
})

export default memo(function IVPHatches() {
  const { t } = useI18n('Forms')
  const data = useFormQuestionMRData()
  const rows = useResponseRows([])

  function addRow() {
    rows.add({ [HAS_MR_DATA]: FALSE })
  }

  function canRemove(index: number) {
    return Array.isArray(data) && index >= data.length
  }

  return (
    <div className="flex flex-col gap-sm @container">
      <div className="text-body-sm font-bold">{t('IVPHatches.title')}</div>
      <div className="flex flex-col gap-md">
        {Array.isArray(data) && data.length === 0 && (
          <Alert level="warning" urgency="inline" compact>
            {t('noMRData')}
          </Alert>
        )}
        {rows.array.length === 0 ? (
          <Button
            text={t('addData')}
            variant="neutral"
            className="self-end"
            onClick={addRow}
          />
        ) : (
          <PatternWithRows
            RowComponent={IVPHatchesRow}
            emptiable
            onAdd={addRow}
            isRowRemovable={canRemove}
          />
        )}
      </div>
    </div>
  )
})
