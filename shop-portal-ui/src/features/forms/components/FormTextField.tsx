import { Json } from '@/types/Json'
import { FormField } from '@gatx-corp/platform-one-common/components/FormField'
import { TextInput } from '@gatx-corp/platform-one-common/components/TextInput'
import clsx from 'clsx'
import { z } from 'zod'
import { useDebouncedState } from '../hooks/useDebouncedState'
import { useFormHasEditPermission } from '../providers/FormProvider'
import FormFieldContainer from './FormFieldContainer'

type Props = {
  label: string
  value: Json | undefined
  onChange?: (value: Json) => void
  error?: string
  warning?: string
  readOnly?: boolean
  disabled?: boolean
  hidden?: boolean
  help?: string
  placeholder?: string
  /**
   * Whether the field should be printed with a fixed width or a full width.
   * Not noticeable when `printBorder` is false.
   */
  printWidth?: 'fixed' | 'full'
  /**
   * Whether the field should be printed with a border.
   */
  printBorder?: boolean
}

const FormTextField = ({
  label,
  value: json = '',
  onChange = () => undefined,
  error,
  warning,
  readOnly,
  disabled,
  hidden = false,
  help,
  printWidth = 'fixed',
  printBorder = true,
  placeholder,
}: Props) => {
  const [value, handleChange] = useDebouncedState<string>(json, onChange, (v) =>
    z.coerce.string().parse(v),
  )

  const hasEditPermission = useFormHasEditPermission()

  return (
    <FormFieldContainer
      hidden={hidden}
      screen={
        <FormField
          label={label}
          readOnly={readOnly}
          disabled={!hasEditPermission || disabled}
          error={error}
          warning={warning}
          help={help}
        >
          <TextInput
            value={value}
            onChange={handleChange}
            placeholder={placeholder}
          />
        </FormField>
      }
      print={
        <FormField label={label}>
          <div
            className={clsx(
              'data-[empty=false]:contents data-[empty=false]:blank-print:block h-[25px] text-center rounded-lg',
              {
                'w-full': printWidth === 'full',
                'w-[100px]': printWidth === 'fixed',
                'border border-light-9': printBorder,
              },
            )}
            data-empty={!value}
          >
            <span className="align-middle blank-print:hidden">{value}</span>
          </div>
        </FormField>
      }
    />
  )
}

export default FormTextField
