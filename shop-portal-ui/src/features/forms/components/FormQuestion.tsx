import { <PERSON><PERSON> } from '@/types/Json'
import { i18n, useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { FC, PropsWithChildren } from 'react'
import { Trans } from 'react-i18next'
import { z } from 'zod'
import CleaningGeneral from '../patterns/cleaning-confirmation/components/CleaningGeneral'
import CleaningHighPressure from '../patterns/cleaning-confirmation/components/CleaningHighPressure'
import CleaningHopper from '../patterns/cleaning-confirmation/components/CleaningHopper'
import CleaningOther from '../patterns/cleaning-confirmation/components/CleaningOther'
import Decal from '../patterns/Decal'
import General from '../patterns/General'
import IVPAirPiping from '../patterns/IVPAirPiping'
import IVPBuilderLot from '../patterns/IVPBuilderLot'
import { IVPCastingDate } from '../patterns/IVPCastingDate'
import IVPCommonEquipped from '../patterns/IVPCommonEquipped'
import IVPCommonEquippedText from '../patterns/IVPCommonEquippedText'
import IVPCommonNotEquipped from '../patterns/IVPCommonNotEquipped'
import IVPExteriorPaint from '../patterns/IVPExteriorPaint'
import IVPHatches from '../patterns/IVPHatches'
import IVPLightweight from '../patterns/IVPLightweight'
import IVPOutletGateNotEquipped from '../patterns/IVPOutletGateNotEquipped'
import IVPTruck from '../patterns/IVPTruck'
import None from '../patterns/None'
import ProgConfAffirm from '../patterns/ProgConfAffirm'
import WhyMade from '../patterns/WhyMade'
import {
  useFormHasEditPermission,
  useFormResponse,
  useFormSignatures,
} from '../providers/FormProvider'
import FormQuestionProvider, {
  useFormQuestion,
  useFormQuestionResponseOption,
} from '../providers/FormQuestionProvider'
import {
  FormContent,
  FormResponseOption,
  GLOBAL_FORM_SIGNATURE_ID,
  type FormQuestion,
} from '../types/Form'
import { FormResponsePattern } from '../types/FormResponsePattern'
import FormChoiceGroupField from './FormChoiceGroupField'

// TODO: Isolate response pattern mappings to their templates
const FORM_RESPONSE_PATTERNS: Partial<Record<FormResponsePattern, FC>> = {
  [FormResponsePattern.CLEANING_GENERAL]: CleaningGeneral,
  [FormResponsePattern.CLEANING_HIGH_PRESSURE]: CleaningHighPressure,
  [FormResponsePattern.CLEANING_HOPPER]: CleaningHopper,
  [FormResponsePattern.CLEANING_OTHER]: CleaningOther,
  [FormResponsePattern.DECAL]: Decal,
  [FormResponsePattern.PROGR_CONF_AFFIRMATION]: ProgConfAffirm,
  [FormResponsePattern.GENERAL]: General,
  [FormResponsePattern.IVP_COMMON_EQUIPPED]: IVPCommonEquipped,
  [FormResponsePattern.IVP_LIGHTWEIGHT]: IVPLightweight,
  [FormResponsePattern.IVP_COMMON_EQUIPPED_TEXT]: IVPCommonEquippedText,
  [FormResponsePattern.IVP_COMMON_NOT_EQUIPPED]: IVPCommonNotEquipped,
  [FormResponsePattern.NONE]: None,
  [FormResponsePattern.IVP_BUILDER_LOT]: IVPBuilderLot,
  [FormResponsePattern.IVP_HATCHES]: IVPHatches,
  [FormResponsePattern.WHY_MADE]: WhyMade,
  [FormResponsePattern.IVP_TRUCK]: IVPTruck,
  [FormResponsePattern.IVP_EXTERIOR_PAINT]: IVPExteriorPaint,
  [FormResponsePattern.IVP_OUTLET_GATE_NOT_EQUIPPED]: IVPOutletGateNotEquipped,
  [FormResponsePattern.IVP_CASTING_DATE]: IVPCastingDate,
  [FormResponsePattern.IVP_AIR_PIPING]: IVPAirPiping,
}

const Pattern = ({ option }: { option: FormResponseOption }) => {
  const Component = FORM_RESPONSE_PATTERNS[option.pattern]

  return Component ? (
    <Component />
  ) : (
    <div>Response Pattern {option.pattern} not Implemented</div>
  )
}

const QuestionComments = () => {
  const { t } = useI18n('Forms')
  const question = useFormQuestion()
  const [response] = useFormResponse()

  const checksBannerTitle = t('checks.Banner.title') as string
  const checksBannerNotice = t('checks.Banner.notice') as string

  return (
    Boolean(response[question.id].comments.length) && (
      <ul className="flex flex-col gap-sm blank-print:hidden">
        {response[question.id].comments.map((c) => (
          <li key={c}>
            <div className="contents print:hidden">
              <Alert
                level="info"
                urgency="inline"
                title={checksBannerTitle}
                compact
                className="print:hidden"
              >
                {`"${c}" ${checksBannerNotice}`}
              </Alert>
            </div>
            <div className="hidden print:block">
              {`${checksBannerTitle} — "${c}" ${checksBannerNotice}`}
            </div>
          </li>
        ))}
      </ul>
    )
  )
}

const SelectedPattern = () => {
  const [responseOption] = useFormQuestionResponseOption()

  return responseOption ? (
    <>
      <QuestionComments />
      <Pattern option={responseOption} />
    </>
  ) : (
    <></>
  )
}

const MultipleChoiceQuestion = () => {
  const { t } = useI18n('Forms')
  const question = useFormQuestion()
  const [option, setOption] = useFormQuestionResponseOption()
  const signatures = useFormSignatures()
  const hasEditPermission = useFormHasEditPermission()

  const options = question.responseOptions.map((option) => ({
    label: option.name,
    value: option.id.toString(),
    metadata: {},
  }))

  const onChange = (value: Json) => {
    const id = z.coerce.number().parse(value)

    if (id) {
      setOption(
        question.responseOptions.find((option) => option.id === id)!,
      ).catch((e: unknown) => {
        console.error(e)
      })
    }
  }

  const isReadOnly = [question.id, GLOBAL_FORM_SIGNATURE_ID].some((id) =>
    signatures.get(id.toString()),
  )

  const isBlank = option == undefined

  return (
    <div className="space-y-md">
      <div className="customer-print:hidden">
        <FormChoiceGroupField
          label={t('response')}
          value={option?.id.toString()}
          options={options}
          onChange={onChange}
          printOptions={options}
          readOnly={isReadOnly}
          disabled={!hasEditPermission}
        />
      </div>
      <div className="hidden customer-print:block">{option?.name ?? '-'}</div>

      <QuestionComments />

      {question.responseOptions.map((o) => (
        <div
          key={o.id}
          className="contents blank-print:block data-[hidden=true]:hidden blank-print:data-[hidden=true]:block"
          aria-hidden={isBlank || option.id !== o.id}
          data-hidden={isBlank || option.id !== o.id}
          data-print-type={isBlank ? 'blank' : undefined}
        >
          {o.pattern !== FormResponsePattern.NONE && (
            <div className="hidden text-xs blank-print:block">
              <Trans
                i18n={i18n}
                ns="Forms"
                i18nKey="questionResponseHint"
                values={{ name: o?.name }}
                components={{
                  strong: <span className="font-bold" />,
                }}
              />
            </div>
          )}
          <Pattern option={o} />
        </div>
      ))}
    </div>
  )
}

type Props = PropsWithChildren<{
  question: FormQuestion
  /**
   * FIXME: onPatternChange should not expect the form's response. See comment in FormQuestionProvider.
   */
  onPatternChange?: (
    p: FormResponsePattern | null,
    r: FormContent['response'],
  ) => void
}>

const FormQuestion = ({ question, onPatternChange }: Props) => {
  return (
    <FormQuestionProvider question={question} onPatternChange={onPatternChange}>
      {question.responseOptions.length > 1 ? (
        <MultipleChoiceQuestion />
      ) : (
        <SelectedPattern />
      )}
    </FormQuestionProvider>
  )
}

export default FormQuestion
