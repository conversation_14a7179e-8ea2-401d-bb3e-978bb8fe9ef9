import { J<PERSON> } from '@/types/Json'
import { FormField } from '@gatx-corp/platform-one-common/components/FormField'
import {
  Format,
  formatNumber,
  NumberInput,
} from '@gatx-corp/platform-one-common/components/NumberInput'
import { z } from 'zod'
import { useDebouncedState } from '../hooks/useDebouncedState'
import { useFormHasEditPermission } from '../providers/FormProvider'
import FormFieldContainer from './FormFieldContainer'

type Props = {
  label: string
  value: Json | undefined
  onChange: (value: Json) => void
  error?: string
  min?: number
  max?: number
  step?: number
  readOnly?: boolean
  disabled?: boolean
  hidden?: boolean
  help?: string
  format?: Format
  unit?: string
}

const FormNumberField = ({
  label,
  value: json,
  onChange,
  error,
  min,
  max,
  step,
  readOnly,
  disabled,
  format = 'decimal',
  unit,
  hidden = false,
  help,
}: Props) => {
  const [value, handleChange] = useDebouncedState<number | null | undefined>(
    json!,
    onChange,
    (v) => z.coerce.number().nullish().parse(v),
  )

  const hasEditPermission = useFormHasEditPermission()

  return (
    <FormFieldContainer
      hidden={hidden}
      screen={
        <FormField
          label={label}
          readOnly={readOnly}
          error={error}
          help={help}
          disabled={!hasEditPermission || disabled}
        >
          <NumberInput
            step={step ?? 1}
            format={format}
            unit={unit}
            min={min}
            max={max}
            value={value as number | undefined}
            onChange={handleChange}
          />
        </FormField>
      }
      print={
        <FormField label={label}>
          <div className="w-[100px] h-[25px] justify-center flex items-center rounded-lg border border-light-9 blank-print:content-hidden">
            {value && formatNumber(value, format, step, unit)}
          </div>
        </FormField>
      }
    />
  )
}

export default FormNumberField
