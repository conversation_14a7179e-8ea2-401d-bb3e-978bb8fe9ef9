import { J<PERSON> } from '@/types/Json'
import { DatePicker } from '@gatx-corp/platform-one-common/components/DatePicker'
import { FormField } from '@gatx-corp/platform-one-common/components/FormField'
import clsx from 'clsx'
import { useFormHasEditPermission } from '../providers/FormProvider'
import Form<PERSON>ieldContainer from './FormFieldContainer'

type Props = {
  label: string
  value: Json | undefined
  onChange: (value: Json) => void
  error?: string
  warning?: string
  readOnly?: boolean
  disabled?: boolean
  hidden?: boolean
  help?: string
  minDate?: string
  maxDate?: string
  /**
   * Whether the field should be printed with a fixed width or a full width.
   */
  printWidth?: 'fixed' | 'full'
}

const FormDateField = ({
  label,
  value,
  onChange,
  error,
  warning,
  readOnly,
  disabled,
  hidden = false,
  help,
  printWidth = 'fixed',
  minDate,
  maxDate,
}: Props) => {
  const hasEditPermission = useFormHasEditPermission()

  return (
    <FormFieldContainer
      hidden={hidden}
      screen={
        <FormField
          label={label}
          readOnly={readOnly}
          disabled={!hasEditPermission || disabled}
          error={error}
          warning={warning}
          help={help}
        >
          <DatePicker
            value={value as string | undefined}
            onChange={onChange as (v: string | undefined) => void}
            min={minDate}
            max={maxDate}
          />
        </FormField>
      }
      print={
        <FormField label={label}>
          <div
            className={clsx(
              'data-[empty=false]:contents data-[empty=false]:blank-print:block h-[25px] text-center rounded-lg',
              {
                'w-full': printWidth === 'full',
                'w-[100px]': printWidth === 'fixed',
              },
            )}
            data-empty={!value}
          >
            <span className="align-middle blank-print:hidden">
              {value as string}
            </span>
          </div>
        </FormField>
      }
    />
  )
}

export default FormDateField
