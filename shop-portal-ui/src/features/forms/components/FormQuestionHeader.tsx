import { useI18n } from '@gatx-corp/platform-one-common'
import { useFormReferenceDocuments } from '../providers/FormProvider'
import { FormQuestion } from '../types/Form'
import ProcedureButton from './ProcedureButton'

type Props = {
  question: FormQuestion
  formTemplateName: string
}

const FormQuestionHeader = ({ question, formTemplateName }: Props) => {
  const { t } = useI18n('Forms')
  const formDocuments = useFormReferenceDocuments()
  const documents = formDocuments.filter(
    (doc) => doc.dciQuestionId === question.id,
  )

  const inspectionMethod = `${formTemplateName}.inspectionMethod.${question.inspMethod}`

  return (
    <div className="flex justify-between">
      <dl>
        <dt className="whitespace-nowrap customer-print:hidden">
          {t(`common.inspectionMethod`)}
        </dt>
        <dd>{t(inspectionMethod, { defaultValue: '' })}</dd>
      </dl>
      <div className="flex flex-col gap-md">
        {documents?.map((doc) => (
          <ProcedureButton key={doc.referenceDocId} document={doc} />
        ))}
      </div>
    </div>
  )
}

export { FormQuestionHeader }
