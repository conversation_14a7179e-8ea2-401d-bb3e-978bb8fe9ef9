import { formatDate } from '@/utils/dates'
import { useI18n } from '@gatx-corp/platform-one-common'
import { useHasPermission } from '@gatx-corp/platform-one-common/auth'
import { Badge } from '@gatx-corp/platform-one-common/components/Badge'
import { Button } from '@gatx-corp/platform-one-common/components/Button'
import { ChoiceControl } from '@gatx-corp/platform-one-common/components/ChoiceControl'
import { isAxiosError } from 'axios'
import { useId, useState } from 'react'
import {
  useCompleteForm,
  useForm,
  useFormHasEditPermission,
  useFormServiceEvent,
  useFormSignatures,
  useSignFormSection,
} from '../providers/FormProvider'
import { FormSignature } from '../types/Form'

function useLabel(id: string) {
  const { t } = useI18n('Forms')
  const form = useForm()

  return t(
    [
      `${form.template}.${id}.signature.label`,
      `${form.template}.signature.label`,
    ],
    {
      defaultValue: '[signed]',
    },
  )
}

function useAcknowledgment(id: string) {
  const { t } = useI18n('Forms')
  const form = useForm()
  return t(
    [
      `${form.template}.${id}.signature.acknowledgment`,
      `${form.template}.signature.acknowledgment`,
    ],
    {
      defaultValue: '[missing acknowledgment]',
    },
  )
}

function formatSignature(signature: FormSignature) {
  return `${signature.signedBy.name} ${formatDate(signature.signedAt)}`
}

const SUPPORT_ERROR_MESSAGE =
  'The signature attempt failed. Please contact support for assistance.'

type SignatureError = {
  status?: number | string
  message: string
}

type Props = {
  id: string
  signable?: boolean
  unsignable?: boolean
  onError?: (error: SignatureError) => void
  onSign?: () => void
  isFinal?: boolean
}

const FormSignatureInput = ({
  id,
  signable,
  unsignable,
  onError,
  onSign,
  isFinal,
}: Props) => {
  const { t } = useI18n('Forms')

  const buttonId = useId()

  const signatures = useFormSignatures()
  const serviceEvent = useFormServiceEvent()

  const [acknowledged, setAcknowledged] = useState(false)

  const { complete, disabled, alert } = useCompleteForm()

  const { signForm } = useSignFormSection()

  const label = useLabel(id)
  const acknowledgment = useAcknowledgment(id)

  async function handleSign() {
    try {
      if (isFinal) {
        await complete(id)
      } else {
        await signForm(id)
      }
      onSign?.()
    } catch (e) {
      if (isAxiosError(e)) {
        if (e.response) {
          onError?.({ status: e.status, message: SUPPORT_ERROR_MESSAGE })
        } else if (e.request) {
          onError?.({ status: e.code, message: SUPPORT_ERROR_MESSAGE })
        } else {
          onError?.({ message: SUPPORT_ERROR_MESSAGE })
        }
      } else if (e instanceof Error) {
        onError?.({ message: e.message })
      } else {
        onError?.({ message: String(e) })
      }
    }
  }

  const hasEditPermission = useFormHasEditPermission()

  const hasPermission = useHasPermission(serviceEvent.shop)
  const canSign = hasPermission('FORM_SIGN')
  const canUnsign = hasPermission('FORM_UNSIGN')

  return (
    <div className="contents print:hidden">
      {isFinal && !signatures.completed(id) && disabled && <>{alert}</>}
      {signatures.completed(id) ? (
        <section className="space-y-lg">
          <div className="space-y-md">
            <div>
              {t('SignatureInput.labelByName', {
                label,
                name: formatSignature(signatures.map[id]),
              })}
            </div>
            <div className="text-body-sm">
              {t('SignatureInput.signedSectionMessage')}
            </div>
          </div>

          <Button
            id={buttonId}
            data-section-id={id}
            data-action={isFinal ? 'unsign-final' : 'unsign'}
            variant="outline"
            text={t('SignatureInput.unsignLabel', { label })}
            onClick={() => {
              signatures.unsign(id).catch(console.error)
            }}
            aria-label={t('SignatureInput.unsignLabel', { label })}
            aria-pressed={false}
            disabled={!unsignable || !canUnsign || !hasEditPermission}
          />
        </section>
      ) : (
        <section className="border border-light-8 bg-light-4 dark:bg-dark-7 dark:border-dark-8 rounded pt-md px-md pb-lg space-y-lg">
          <ChoiceControl
            variant="classic"
            type="checkbox"
            name={`${id}-acknowledgement`}
            value={`${id}-acknowledgement`}
            label={acknowledgment}
            isSelected={acknowledged}
            onChange={() => setAcknowledged((prev) => !prev)}
            disabled={!hasEditPermission || !canSign || (isFinal && disabled)}
          />
          <div className="px-lg">
            <Button
              id={buttonId}
              data-section-id={id}
              variant="primary"
              text={t('SignatureInput.confirmLabel', { label })}
              onClick={() => {
                handleSign().catch(console.error)
              }}
              aria-label={t('SignatureInput.confirmLabel', { label })}
              aria-pressed
              disabled={
                !hasEditPermission ||
                !canSign ||
                !acknowledged ||
                !signable ||
                (isFinal && disabled)
              }
            />
          </div>
        </section>
      )}
    </div>
  )
}

const FormSignatureBadge = ({
  id,
  hidden,
}: {
  id: string
  hidden?: boolean
}) => {
  const { t } = useI18n('Forms')
  const signatures = useFormSignatures()

  const label = useLabel(id)

  return (
    <>
      {!hidden && (
        <div className="contents print:hidden">
          {signatures.completed(id) ? (
            <Badge
              variant="success"
              label={t('SignatureInput.signedByName', {
                name: formatSignature(signatures.map[id]),
              })}
            />
          ) : (
            <Badge variant="warning" label={t('inProgress')} />
          )}
        </div>
      )}
      <div className="hidden print:flex items-center gap-sm print:text-body-sm whitespace-nowrap h-[20px]">
        <span>{t('SignatureInput.nameBy', { name: label })}</span>
        <span className="rounded bg-primary-1 customer-print:bg-[unset] text-[black] customer-print:text-[unset] px-sm customer-print:px-0 py-[2px] empty:w-[200px] blank-print:w-[200px] customer-print:w-fit h-full flex items-center blank-print:content-hidden">
          {signatures.map[id]?.signedBy.name ?? ''}
        </span>
        <span>{t('SignatureInput.dateSigned')}</span>
        <span className="rounded bg-primary-1 customer-print:bg-[unset] text-[black] customer-print:text-[unset] px-sm customer-print:px-0 py-[2px] w-[100px] customer-print:w-fit h-full flex items-center blank-print:content-hidden">
          {formatDate(signatures.map[id]?.signedAt)}
        </span>
      </div>
    </>
  )
}

export default FormSignatureInput
export { FormSignatureBadge }
export type { SignatureError }
