import {
  useFormQuestion,
  useResponseRows,
} from '@/features/forms/providers/FormQuestionProvider'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Button } from '@gatx-corp/platform-one-common/components/Button'
import clsx from 'clsx'
import { ComponentType, memo } from 'react'
import {
  useFormHasEditPermission,
  useFormSignatures,
} from '../../providers/FormProvider'

const BLANK_PRINT_ROWS = 3

export type PatternRowProps = {
  index: number
  /**
   * FIXME: This is a temporary solution while the `FormSelectField` has side-effects over the data.
   */
  noSideEffects?: boolean
}

type Props = {
  RowComponent: ComponentType<PatternRowProps>
  emptiable?: boolean
  removable?: boolean
  onAdd?: () => void
  isRowRemovable?: (index: number) => boolean
}

export default memo(function PatternWithRows({
  RowComponent,
  emptiable = false,
  removable = false,
  isRowRemovable = () => removable,
  onAdd,
}: Props) {
  const { t } = useI18n('Forms')

  const question = useFormQuestion()
  const signatures = useFormSignatures()
  const completed = signatures.completed(question.id.toString())

  const rows = useResponseRows()

  const hasEditPermission = useFormHasEditPermission()

  function handleAdd() {
    if (onAdd) {
      onAdd()
    } else {
      rows.add()
    }
  }

  return (
    <div className="grid grid-cols-[1fr_auto] print:grid-cols-1 gap-lg print:gap-sm">
      <ul className="contents blank-print:hidden">
        {rows.array.map((_, index) => {
          const showRemove =
            removable &&
            isRowRemovable?.(index) &&
            (emptiable || rows.array.length > 1)

          return (
            <li key={index} className="contents">
              <div
                className={clsx({
                  'col-span-full': !showRemove,
                })}
              >
                <RowComponent index={index} />
              </div>
              {showRemove && !completed && (
                <div className="mt-xl print:hidden">
                  <Button
                    text={t('common.removeRow')}
                    iconOnly
                    icon="XLg"
                    variant="neutral"
                    aria-label={t('common.removeRow')}
                    className="h-[40px]"
                    onClick={() => rows.remove(index)}
                    disabled={!hasEditPermission}
                  />
                </div>
              )}
              <hr className="border-light-7 dark:border-dark-5 col-span-full" />
            </li>
          )
        })}
      </ul>

      <div className="hidden blank-print:contents">
        {Array.from({ length: BLANK_PRINT_ROWS }).map((_, index) => (
          <RowComponent
            // TODO: review if starting from the next index to the current is required
            // once we remove `noSideEffects` and solve the underlying issue
            key={index + rows.array.length}
            index={index + rows.array.length}
            noSideEffects
          />
        ))}
      </div>

      {!completed && (
        <div className="print:hidden col-span-2 ml-auto flex gap-sm">
          <Button
            text={t('common.addRow')}
            variant="neutral"
            onClick={handleAdd}
            disabled={!hasEditPermission}
          />
        </div>
      )}
    </div>
  )
})
