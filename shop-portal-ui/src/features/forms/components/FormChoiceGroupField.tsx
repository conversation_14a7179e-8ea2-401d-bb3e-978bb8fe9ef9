import { <PERSON><PERSON> } from '@/types/Json'
import { Size } from '@gatx-corp/platform-one-common/components/ChoiceControl'
import { ChoiceGroup } from '@gatx-corp/platform-one-common/components/ChoiceGroup'
import { FormField } from '@gatx-corp/platform-one-common/components/FormField'
import clsx from 'clsx'
import { Check, CircleFill } from 'react-bootstrap-icons'
import { useFormHasEditPermission } from '../providers/FormProvider'
import { Option } from '../types/FormOptions'
import FormFieldContainer from './FormFieldContainer'

type Props = {
  label: string
  labelScreenreaderOnly?: boolean
  value: Json | undefined
  onChange: (value: Json) => void
  error?: string
  warning?: string
  readOnly?: boolean
  options?: Option[]
  size?: Size
  multi?: boolean
  layout?: 'vertical' | 'inline'
  variant?: 'classic' | 'chip'
  printOptions?: Option[]
  hidden?: boolean
  help?: string
  disabled?: boolean
}

const FormChoiceGroupField = (props: Props) => {
  const {
    label,
    labelScreenreaderOnly,
    value,
    onChange,
    error,
    warning,
    readOnly,
    size,
    options = [],
    multi,
    layout,
    variant,
    hidden = false,
    help,
    disabled,
  } = props

  const hasEditPermission = useFormHasEditPermission()

  return (
    <FormFieldContainer
      hidden={hidden}
      screen={
        <ChoiceGroup<Json>
          label={label}
          labelScreenreaderOnly={labelScreenreaderOnly}
          value={value}
          onChange={onChange}
          error={error}
          warning={warning}
          readOnly={readOnly}
          size={size}
          options={options}
          multiSelect={multi as false}
          layout={layout}
          variant={variant}
          help={help}
          disabled={!hasEditPermission || disabled}
        />
      }
      print={<PrintableVersion {...props} />}
    />
  )
}

const PrintableVersion = ({
  value,
  label,
  layout,
  labelScreenreaderOnly,
  printOptions = [],
  options = [],
}: Props) => {
  const values = [value].flat().filter(Boolean)

  function isSelected(option: Option) {
    return values.some((v) => v === option.value)
  }

  return (
    <FormField label={label} labelScreenreaderOnly={labelScreenreaderOnly}>
      <ul
        data-layout={layout}
        className={clsx(
          'gap-sm',
          printOptions.length > 4 ? 'grid grid-cols-4' : 'flex',
        )}
      >
        {printOptions.map((option) => {
          const selected = isSelected(option)
          const blankOnly = !options.some((o) => o.value === option.value)

          return (
            <li
              key={option.label}
              className="flex items-center gap-sm data-[blank-only=true]:print:hidden data-[blank-only=true]:blank-print:flex"
              data-blank-only={blankOnly}
            >
              <div
                className={clsx(
                  'flex items-center justify-center size-md',
                  'border border-dark-5 text-[white]',
                  'selected:bg-dark-5 selected:blank-print:bg-[unset] selected:blank-print:content-hidden',
                  layout === 'vertical' ? 'rounded-full p-xs' : 'rounded-md',
                )}
                data-selected={selected}
              >
                {layout === 'vertical' ? <CircleFill /> : <Check />}
              </div>
              <label className="whitespace-nowrap">{option.label}</label>
            </li>
          )
        })}
      </ul>
    </FormField>
  )
}

export default FormChoiceGroupField
