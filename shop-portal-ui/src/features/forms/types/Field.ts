import { z } from 'zod'
import number from './FormNumber'

export const Integer = number().int()
export const Float = number().step(0.01)

export const PositiveInteger = Integer.min(0).step(1)
export const PositiveFloat = Float.min(0)

export const Percentage = Float.min(
  0,
  'Number must be greater than or equal to 0%',
).max(1, 'Number must be less than or equal to 100%')

export const NonBlankString = z.string().min(1, { message: 'Required' })

export const StringWithMaxLength = (max: number) =>
  z.string().max(max, `String must contain at most ${max} character(s)`)

export const RequiredStringWithMaxLength = (max: number) =>
  NonBlankString.max(max, `String must contain at most ${max} character(s)`)

export const AlphanumericString = z.string().regex(/^[A-Za-z0-9]+$/)
