import { z, ZodSchema } from 'zod'
import { CleaningGeneralResponse } from '../patterns/cleaning-confirmation/types/CleaningGeneral'
import { CleaningHighPressureResponse } from '../patterns/cleaning-confirmation/types/CleaningHighPressure'
import { CleaningHopperResponse } from '../patterns/cleaning-confirmation/types/CleaningHopper'
import { CleaningOtherResponse } from '../patterns/cleaning-confirmation/types/CleaningOther'
import { DecalResponse } from '../patterns/Decal/types/Decal'
import { GeneralResponse } from '../patterns/General/types/General'
import { IVPAirPiping } from '../patterns/IVPAirPiping/types/IVPAirPiping'
import { IVPBuilderLot } from '../patterns/IVPBuilderLot/types/IVPBuilderLot'
import { IVPCastingDate } from '../patterns/IVPCastingDate/types/IVPCastingDate'
import { IVPCommonEquipped } from '../patterns/IVPCommonEquipped/types/IVPCommonEquipped'
import { IVPCommonEquippedText } from '../patterns/IVPCommonEquippedText/types/IVPCommonEquippedText'
import { IVPCommonNotEquipped } from '../patterns/IVPCommonNotEquipped/types/IVPCommonNotEquipped'
import { IVPExteriorPaint } from '../patterns/IVPExteriorPaint/types/IVPExteriorPaint'
import { IVPHatches } from '../patterns/IVPHatches/types/IVPHatches'
import { IVPLightweight } from '../patterns/IVPLightweight/types/IVPLightweight'
import { IVPOutletGateNotEquipped } from '../patterns/IVPOutletGateNotEquipped/types/IVPOutletGateNotEquipped'
import { IVPTruck } from '../patterns/IVPTruck/types/IVPTruck'
import { ProgConfAffirmResponse } from '../patterns/ProgConfAffirm/types/ProgConfAffirm'
import { WhyMadeResponse } from '../patterns/WhyMade/types/WhyMade'

export enum FormResponsePattern {
  CLEANING_GENERAL = 'ClGeneral',
  CLEANING_HIGH_PRESSURE = 'ClHighPressure',
  CLEANING_HOPPER = 'ClHopper',
  CLEANING_OTHER = 'ClOther',
  CLEANING_DATE_TIME = 'ClDateTime',
  DECAL = 'Decal',
  EXT_COATING_TU = 'ExtCoatTU',
  GENERAL = 'General',
  IVP_COMMON_EQUIPPED = 'IVPCommonEquipped',
  IVP_LIGHTWEIGHT = 'IVPLightweight',
  IVP_COMMON_EQUIPPED_TEXT = 'IVPCommonEquippedText',
  IVP_COMMON_NOT_EQUIPPED = 'IVPCommonNotEquipped',
  NONE = 'NONE',
  TCID_2019 = 'TCID2019',
  JACKET_INSULATION = 'JacketInsulation',
  PROGR_CONF_AFFIRMATION = 'ProgConfAffirm',
  IVP_HATCHES = 'IVPHatches',
  WHY_MADE = 'WhyMade',
  IVP_BUILDER_LOT = 'IVPBuilderLot',
  IVP_TRUCK = 'IVPTruck',
  IVP_EXTERIOR_PAINT = 'IVPExteriorPaint',
  IVP_OUTLET_GATE_NOT_EQUIPPED = 'IVPOutletGateNotEquipped',
  IVP_CASTING_DATE = 'IVPCastingDate',
  IVP_AIR_PIPING = 'IVPAirPiping',
}

export const RESPONSE_SCHEMA_BY_PATTERN: Partial<
  Record<FormResponsePattern, ZodSchema>
> = {
  [FormResponsePattern.CLEANING_GENERAL]: CleaningGeneralResponse,
  [FormResponsePattern.CLEANING_HIGH_PRESSURE]: CleaningHighPressureResponse,
  [FormResponsePattern.CLEANING_HOPPER]: CleaningHopperResponse,
  [FormResponsePattern.CLEANING_OTHER]: CleaningOtherResponse,
  [FormResponsePattern.DECAL]: DecalResponse,
  [FormResponsePattern.PROGR_CONF_AFFIRMATION]: ProgConfAffirmResponse,
  [FormResponsePattern.GENERAL]: GeneralResponse,
  [FormResponsePattern.IVP_COMMON_EQUIPPED]: IVPCommonEquipped,
  [FormResponsePattern.IVP_LIGHTWEIGHT]: IVPLightweight,
  [FormResponsePattern.IVP_COMMON_EQUIPPED_TEXT]: IVPCommonEquippedText,
  [FormResponsePattern.IVP_COMMON_NOT_EQUIPPED]: IVPCommonNotEquipped,
  [FormResponsePattern.IVP_AIR_PIPING]: IVPAirPiping,
  [FormResponsePattern.NONE]: z.null(),
  [FormResponsePattern.IVP_HATCHES]: IVPHatches,
  [FormResponsePattern.WHY_MADE]: WhyMadeResponse,
  [FormResponsePattern.IVP_BUILDER_LOT]: IVPBuilderLot,
  [FormResponsePattern.IVP_TRUCK]: IVPTruck,
  [FormResponsePattern.IVP_EXTERIOR_PAINT]: IVPExteriorPaint,
  [FormResponsePattern.IVP_OUTLET_GATE_NOT_EQUIPPED]: IVPOutletGateNotEquipped,
  [FormResponsePattern.IVP_CASTING_DATE]: IVPCastingDate,
}
