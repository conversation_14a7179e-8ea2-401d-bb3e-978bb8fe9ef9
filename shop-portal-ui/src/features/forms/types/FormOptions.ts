import {
  addIssueToContext,
  arrayOutputType,
  INVALID,
  ParseInput,
  ParseReturnType,
  z,
  ZodArray,
  ZodIssueCode,
  ZodLiteral,
  ZodNever,
  ZodType,
  ZodTypeDef,
  ZodUnion,
} from 'zod'
import { isStringArray } from '../utils/schema'

type Option = {
  label: string
  value: string
  metadata: Record<string, unknown>
}

type BaseOptions<T extends string = string, Min extends 1 | 2 = 1> = Readonly<
  Min extends 1
    ? [ZodLiteral<T>, ...ZodLiteral<T>[]]
    : Min extends 2
      ? [ZodLiteral<T>, ZodLiteral<T>, ...ZodLiteral<T>[]]
      : never
>

type Output<Options extends BaseOptions, Multi> = Multi extends true
  ? arrayOutputType<Options[number], 'many'>
  : Options[number]['_output']

type Definition<Multi extends boolean> = ZodTypeDef & {
  options: Option[]
  multi: Multi
  innerType: ZodArray<ZodUnion<BaseOptions<string, 1>>, 'many'> | null
  printable: boolean
} & (
    | {
        async: true
        fetch: (query: Record<string, unknown>) => Promise<Option[]>
        resolved: boolean
      }
    | { async: false }
  )

type InferOptionsTuple<
  T extends
    | Readonly<[string, ...string[]]>
    | Readonly<[Option, ...Option[]]>
    | [],
> =
  T extends Readonly<[string, ...string[]]>
    ? { [K in keyof T]: T[K] extends string ? ZodLiteral<T[K]> : never }
    : T extends Readonly<[Option, ...Option[]]>
      ? {
          [K in keyof T]: T[K] extends Option
            ? ZodLiteral<T[K]['value']>
            : never
        }
      : never

function createLiteralUnionArray(schemas: readonly ZodLiteral<string>[]) {
  return z.union(schemas as BaseOptions<string, 2>).array()
}

export class FormOptions<
  Options extends BaseOptions = BaseOptions,
  Multi extends boolean = false,
> extends ZodType<
  Output<Options, Multi>,
  Definition<Multi>,
  Options[number]['_input']
> {
  constructor({
    options = [],
    multi,
    fetch,
    printable = true,
  }: {
    options: Option[]
    fetch?: (query: Record<string, unknown>) => Promise<Option[]>
    multi: Multi
    printable: boolean
  }) {
    const opts = options.map((o) => z.literal(o.value)) as unknown as Options
    const type = opts.length > 0 ? createLiteralUnionArray(opts) : null

    const def: Definition<Multi> = {
      options,
      innerType: type,
      multi,
      async: false,
      printable,
    }

    super(fetch ? { ...def, async: true, fetch, resolved: false } : def)
  }

  _parse(input: ParseInput): ParseReturnType<this['_output']> {
    const { multi, innerType } = this._def

    if (this._def.async && !this._def.resolved) {
      console.error('Validating with unresolved async schema', this)
    }

    if (innerType === null) {
      addIssueToContext(this._getOrReturnCtx(input), {
        code: ZodIssueCode.custom,
        message: 'No options',
      })
      return INVALID
    }

    if (input.data === undefined && multi) {
      return {
        status: 'valid',
        value: [] as unknown as this['_output'],
      }
    }

    if (!input.data) {
      const ctx = this._getOrReturnCtx(input)
      addIssueToContext(ctx, {
        received: ctx.data as unknown,
        code: ZodIssueCode.invalid_literal,
        message: 'Required',
        expected: this._def.options.map((o) => o.value),
      })
      return INVALID
    }

    const i = [input.data].flat()

    if (
      !(multi && !i.length) &&
      !this._def.options.find((o) => i.includes(o.value))
    ) {
      const opt = this._def.options.map((o) => `"${o.value}"`).join(', ')
      const msg = multi ? 'Must be at least one' : 'Must be one'
      const val = i.map((v) => `"${v}"`).join(', ')
      const ctx = this._getOrReturnCtx(input)
      addIssueToContext(ctx, {
        received: ctx.data as unknown,
        code: ZodIssueCode.invalid_literal,
        message: `${msg} of ${opt}, got "${val}"`,
        expected: this._def.options.map((o) => o.value),
      })
      return INVALID
    }

    const result = multi
      ? innerType._parse(input)
      : innerType._def.type._parse(input)

    return result as ParseReturnType<this['_output']>
  }

  multi(): FormOptions<Options, true> {
    return new FormOptions<Options, true>({
      options: this._def.options,
      multi: true,
      printable: this._def.printable,
    })
  }

  async(
    fetch: (query: Record<string, unknown>) => Promise<Option[]>,
  ): FormOptions<BaseOptions, Multi> {
    return new FormOptions<Options, Multi>({
      options: this._def.options,
      multi: this._def.multi,
      fetch,
      printable: this._def.printable,
    })
  }

  async resolve(query: Record<string, unknown>): Promise<void> {
    if (this._def.async && !this._def.resolved) {
      this._def.options = await this._def.fetch(query)
      this._def.resolved = true
      this._refreshInnerType()
    }
  }

  _refreshInnerType(): void {
    const { options } = this._def

    const opts = options.map((o) => z.literal(o.value))
    this._def.innerType = createLiteralUnionArray(opts)
  }

  $or<T extends string>(
    other: FormOptions<BaseOptions<T>> | string,
  ): FormOptions<BaseOptions<T | Options[number]['_output']>, false> {
    return FormOptions.join([
      this as unknown as FormOptions<BaseOptions<T>>,
      other instanceof FormOptions ? other : options(other),
    ])
  }

  clone(): FormOptions<Options, Multi> {
    return new FormOptions({
      ...this._def,
    })
  }

  nonprintable(): FormOptions<Options, Multi> {
    return new FormOptions({
      ...this._def,
      printable: false,
    })
  }

  static create<
    Options extends
      | readonly [string, ...string[]]
      | readonly [Option, ...Option[]]
      | [],
  >(opts: Options): FormOptions<InferOptionsTuple<Options>, false> {
    let options: Option[]
    if (isStringArray(opts)) {
      options = opts.map((o) => ({ label: o, value: o, metadata: {} }))
    } else {
      options = opts as [Option, ...Option[]]
    }
    return new FormOptions<InferOptionsTuple<Options>, false>({
      options,
      multi: false,
      printable: true,
    })
  }

  static join<T extends string>(
    Schemas: (ZodLiteral<T> | FormOptions<BaseOptions<T>> | ZodNever)[],
  ): FormOptions<BaseOptions<T>> {
    const opts = Schemas.flatMap((Schema) => {
      if (Schema instanceof ZodLiteral) {
        const value = String(Schema._def.value)
        const label = value.charAt(0).toUpperCase() + value.slice(1)

        return [{ label, value, metadata: {} }]
      }

      if (Schema instanceof FormOptions) {
        return Schema._def.options
      }

      if (Schema instanceof ZodNever) {
        return []
      }

      console.error('Form branch discriminator schema not supported:', Schema)

      return []
    })
      .reduce((acc: Map<string, Option>, opt) => {
        if (!acc.has(opt.value)) {
          acc.set(opt.value, opt)
        }
        return acc
      }, new Map<string, Option>())
      .values() as unknown as [Option, ...Option[]]

    return options(...opts) as FormOptions<BaseOptions<T>>
  }
}

export default function options<
  const Options extends
    | Readonly<[string, ...string[]]>
    | Readonly<[Option, ...Option[]]>
    | [],
>(...opts: Options): FormOptions<InferOptionsTuple<Options>, false> {
  return FormOptions.create(opts)
}

const Yes = options('Yes')
const No = options('No')
const NA = options('N/A')
const OK = options('OK')
const True = z.literal('true')
const False = z.literal('false')
const CantTell = options("Can't Tell")
const Change = options('Change')

export { CantTell, Change, False, NA, No, OK, True, Yes }

export type { Option }
