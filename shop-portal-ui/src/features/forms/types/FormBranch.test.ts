import { expectType, TypeEqual } from 'ts-expect'
import { z, ZodBoolean, ZodLiteral, ZodNumber, ZodObject, ZodString } from 'zod'
import { ObjectSchema } from '../utils/schema'
import branch, { FormBranch, merge, mergeAll } from './FormBranch'
import options, { FormOptions } from './FormOptions'
import { Remove } from './FormRemove'

function expectInstanceOf<T>(
  u: unknown,
  constructor: new (...args: any[]) => T,
): asserts u is T {
  expect(u).toBeInstanceOf(constructor)
}

describe('FormBranch', () => {
  it('parses valid input using literal discriminator', () => {
    const Schema = branch(
      ['type'],
      z.object({
        type: z.literal('a'),
        a: z.string(),
      }),
      z.object({
        type: z.literal('b'),
        b: z.number(),
      }),
    )

    type Type = { type: 'a'; a: string } | { type: 'b'; b: number }
    expectType<TypeEqual<z.infer<typeof Schema>, Type>>(true)

    expect(Schema.parse({ type: 'a', a: 'string', b: 1 })).toEqual({
      type: 'a',
      a: 'string',
    })

    expect(Schema.parse({ type: 'b', a: 'string', b: 1 })).toEqual({
      type: 'b',
      b: 1,
    })
  })

  it('parses valid input using FormOptions discriminator', () => {
    const Schema = branch(
      ['type'],
      z.object({
        type: options('a', 'aa'),
        a: z.string(),
      }),
      z.object({
        type: options('b'),
        b: z.number(),
      }),
    )

    type Type = { type: 'a' | 'aa'; a: string } | { type: 'b'; b: number }
    expectType<TypeEqual<z.infer<typeof Schema>, Type>>(true)

    expect(Schema.parse({ type: 'a', a: 'string', b: 1 })).toEqual({
      type: 'a',
      a: 'string',
    })

    expect(Schema.parse({ type: 'aa', a: 'string', b: 1 })).toEqual({
      type: 'aa',
      a: 'string',
    })

    expect(Schema.parse({ type: 'b', a: 'string', b: 1 })).toEqual({
      type: 'b',
      b: 1,
    })
  })

  it('parses valid input using mixed discriminator', () => {
    const Schema = branch(
      ['type'],
      z.object({
        type: options('a', 'aa'),
        a: z.string(),
      }),
      z.object({
        type: z.literal('b'),
        b: z.number(),
      }),
    )

    type Type = { type: 'a' | 'aa'; a: string } | { type: 'b'; b: number }
    expectType<TypeEqual<z.infer<typeof Schema>, Type>>(true)

    expect(Schema.parse({ type: 'a', a: 'string', b: 1 })).toEqual({
      type: 'a',
      a: 'string',
    })

    expect(Schema.parse({ type: 'aa', a: 'string', b: 1 })).toEqual({
      type: 'aa',
      a: 'string',
    })

    expect(Schema.parse({ type: 'b', a: 'string', b: 1 })).toEqual({
      type: 'b',
      b: 1,
    })
  })

  it('parses valid input using nested discriminator', () => {
    const Schema = branch(
      ['nested', 'type'],
      z.object({
        nested: z.object({ type: options('a', 'aa') }),
        a: z.string(),
      }),
      z.object({
        nested: z.object({ type: z.literal('b') }),
        b: z.number(),
      }),
    )

    type Type =
      | { nested: { type: 'a' | 'aa' }; a: string }
      | { nested: { type: 'b' }; b: number }
    expectType<TypeEqual<z.infer<typeof Schema>, Type>>(true)

    expect(Schema.parse({ nested: { type: 'a' }, a: 'string', b: 1 })).toEqual({
      nested: { type: 'a' },
      a: 'string',
    })

    expect(Schema.parse({ nested: { type: 'aa' }, a: 'string', b: 1 })).toEqual(
      {
        nested: { type: 'aa' },
        a: 'string',
      },
    )

    expect(Schema.parse({ nested: { type: 'b' }, a: 'string', b: 1 })).toEqual({
      nested: { type: 'b' },
      b: 1,
    })
  })

  it('parses valid input using branch options', () => {
    const Schema = branch(
      ['foo'],
      merge(
        z.object({
          foo: options('a'),
        }),
        branch(
          ['bar'],
          z.object({ bar: z.literal('aa'), a: z.number() }),
          z.object({ bar: z.literal('ab'), a: z.string() }),
        ),
      ),
      merge(
        z.object({
          foo: options('b'),
        }),
        branch(
          ['bar'],
          z.object({ bar: z.literal('ba'), b: z.number() }),
          z.object({ bar: z.literal('bb'), b: z.string() }),
        ),
      ),
    )

    type Type =
      | { foo: 'a'; bar: 'aa'; a: number }
      | { foo: 'a'; bar: 'ab'; a: string }
      | { foo: 'b'; bar: 'ba'; b: number }
      | { foo: 'b'; bar: 'bb'; b: string }
    expectType<TypeEqual<z.infer<typeof Schema>, Type>>(true)

    expect(Schema.parse({ foo: 'a', bar: 'aa', a: 1 })).toEqual({
      foo: 'a',
      bar: 'aa',
      a: 1,
    })

    expect(Schema.parse({ foo: 'a', bar: 'ab', a: 'string' })).toEqual({
      foo: 'a',
      bar: 'ab',
      a: 'string',
    })

    expect(Schema.parse({ foo: 'b', bar: 'ba', b: 1 })).toEqual({
      foo: 'b',
      bar: 'ba',
      b: 1,
    })

    expect(Schema.parse({ foo: 'b', bar: 'bb', b: 'string' })).toEqual({
      foo: 'b',
      bar: 'bb',
      b: 'string',
    })
  })

  describe('isDiscriminatorPath', () => {
    it('returns true for a path that targets the discriminator', () => {
      const Schema = branch(
        ['nested', 'type'],
        z.object({
          nested: z.object({ type: options('a', 'aa') }),
          a: z.string(),
        }),
        z.object({
          nested: z.object({ type: z.literal('b') }),
          b: z.number(),
        }),
      )

      expect(Schema.isDiscriminatorPath(['nested', 'type'])).toBe(true)
    })

    it('returns false for a path that does not target the discriminator', () => {
      const Schema = branch(
        ['nested', 'type'],
        z.object({
          nested: z.object({ type: options('a', 'aa') }),
          a: z.string(),
        }),
        z.object({
          nested: z.object({ type: z.literal('b') }),
          b: z.number(),
        }),
      )

      expect(Schema.isDiscriminatorPath(['nested'])).toBe(false)
      expect(Schema.isDiscriminatorPath(['nested', 'foo'])).toBe(false)
    })
  })

  describe('getDiscriminatorJointSchema', () => {
    it('returns the joint FormOptions schema for a literal discriminator', () => {
      const Schema = branch(
        ['nested', 'type'],
        z.object({
          nested: z.object({ type: z.literal('a') }),
          a: z.string(),
        }),
        z.object({
          nested: z.object({ type: z.literal('b') }),
          b: z.number(),
        }),
      )

      const Result = Schema.getDiscriminatorJointSchema()

      expect(Result._def.options).toEqual([
        { label: 'A', value: 'a', metadata: {} },
        { label: 'B', value: 'b', metadata: {} },
      ])
    })

    it('returns the joint FormOptions schema for a FormOptions discriminator', () => {
      const Schema = branch(
        ['nested', 'type'],
        z.object({
          nested: z.object({ type: options('a', 'aa') }),
          a: z.string(),
        }),
        z.object({
          nested: z.object({ type: options('b') }),
          b: z.number(),
        }),
      )

      const Result = Schema.getDiscriminatorJointSchema()

      expect(Result._def.options).toEqual([
        { label: 'a', value: 'a', metadata: {} },
        { label: 'aa', value: 'aa', metadata: {} },
        { label: 'b', value: 'b', metadata: {} },
      ])
    })

    it('returns the joint FormOptions schema for a mixed discriminator', () => {
      const Schema = branch(
        ['nested', 'type'],
        z.object({
          nested: z.object({ type: options('a', 'aa') }),
          a: z.string(),
        }),
        z.object({
          nested: z.object({ type: z.literal('b') }),
          b: z.number(),
        }),
      )

      const Result = Schema.getDiscriminatorJointSchema()

      expect(Result._def.options).toEqual([
        { label: 'a', value: 'a', metadata: {} },
        { label: 'aa', value: 'aa', metadata: {} },
        { label: 'B', value: 'b', metadata: {} },
      ])
    })
  })

  describe('getPropertySchema', () => {
    it('returns the discriminator joint schema for the discriminator path', () => {
      const Schema = branch(
        ['nested', 'type'],
        z.object({
          nested: z.object({ type: options('a', 'aa') }),
          a: z.string(),
        }),
        z.object({
          nested: z.object({ type: z.literal('b') }),
          b: z.number(),
        }),
      )

      const [Result, ...rest] = Schema.getPropertySchema([
        'nested',
        'type',
      ]) as [FormOptions]

      expect(rest).toEqual([])
      expect(Result).toBeInstanceOf(FormOptions)
      expect(Result._def.options).toEqual([
        { label: 'a', value: 'a', metadata: {} },
        { label: 'aa', value: 'aa', metadata: {} },
        { label: 'B', value: 'b', metadata: {} },
      ])
    })

    it('returns all posible schemas for a non-discriminator path', () => {
      const Schema = branch(
        ['nested', 'type'],
        z.object({
          nested: z.object({ type: options('a', 'aa'), b: z.number() }),
          a: z.string(),
        }),
        z.object({
          nested: z.object({ type: z.literal('b'), b: z.string() }),
          a: z.number(),
        }),
      )

      const [first, second, ...rest] = Schema.getPropertySchema(['nested', 'b'])

      expect(rest).toEqual([])
      expect(first).toBeInstanceOf(ZodNumber)
      expect(second).toBeInstanceOf(ZodString)
    })
  })

  describe('parseValid', () => {
    it('parses a valid branch', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string() }),
        z.object({ type: z.literal('b'), bar: z.number() }),
      )

      let result = Schema.parseValid({
        type: 'a',
        foo: 'foo',
      })

      expect(result).toStrictEqual({ type: 'a', foo: 'foo' })

      result = Schema.parseValid({
        type: 'b',
        bar: 1,
      })

      expect(result).toStrictEqual({ type: 'b', bar: 1 })
    })

    it('parses a valid branch keeping only the known properties', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string() }),
        z.object({ type: z.literal('b'), bar: z.number() }),
      )

      let result = Schema.parseValid({
        type: 'a',
        foo: 'foo',
        bar: 1,
      })

      expect(result).toStrictEqual({ type: 'a', foo: 'foo' })

      result = Schema.parseValid({
        type: 'b',
        foo: 'foo',
        bar: 1,
      })

      expect(result).toStrictEqual({ type: 'b', bar: 1 })
    })

    it('parses a partially valid branch keeping only the valid properties', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string(), qux: z.number() }),
        z.object({ type: z.literal('b'), bar: z.number(), qux: z.number() }),
      )

      let result = Schema.parseValid({
        type: 'a',
        foo: 'foo',
        bar: 1,
        qux: 'qux',
      })

      expect(result).toStrictEqual({ type: 'a', foo: 'foo' })

      result = Schema.parseValid({
        type: 'b',
        foo: 'foo',
        bar: 1,
        qux: 'qux',
      })

      expect(result).toStrictEqual({ type: 'b', bar: 1 })
    })

    it('parses a partially valid branch recursively', () => {
      const Schema = branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.string(),
          qux: branch(
            ['type'],
            z.object({
              type: z.literal('a'),
              foo: z.string(),
              bar: z.number(),
            }),
            z.object({
              type: z.literal('b'),
              foo: z.string(),
              bar: z.number(),
            }),
          ),
        }),
        z.object({
          type: z.literal('b'),
          bar: z.number(),
          qux: z.number(),
        }),
      )

      let result = Schema.parseValid({
        type: 'a',
        foo: 'foo',
        qux: {
          type: 'a',
          foo: 'foo',
          bar: 'bar',
          qux: null,
        },
      })

      expect(result).toStrictEqual({
        type: 'a',
        foo: 'foo',
        qux: {
          type: 'a',
          foo: 'foo',
        },
      })

      result = Schema.parseValid({
        type: 'a',
        foo: 'foo',
        qux: {
          type: 'b',
          foo: 1,
          bar: 1,
          qux: null,
        },
      })

      expect(result).toStrictEqual({
        type: 'a',
        foo: 'foo',
        qux: {
          type: 'b',
          bar: 1,
        },
      })
    })

    it('parses a invalid branch', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string() }),
        z.object({ type: z.literal('b'), bar: z.number() }),
      )

      const result = Schema.parseValid({
        type: 'c',
        foo: 'foo',
        bar: 'bar',
      })

      expect(result).toStrictEqual({ foo: 'foo' })
    })

    it('parses valid chained branches recursively', () => {
      const Schema = branch(
        ['type1'],
        branch(
          ['type2'],
          z.object({
            type1: z.literal('a'),
            type2: z.literal('a'),
            aa: z.string(),
          }),
          z.object({
            type1: z.literal('a'),
            type2: z.literal('b'),
            ab: z.number(),
          }),
        ),
        branch(
          ['type2'],
          z.object({
            type1: z.literal('b'),
            type2: z.literal('a'),
            ba: z.boolean(),
          }),
          z.object({
            type1: z.literal('b'),
            type2: z.literal('b'),
            bb: z.object({ qux: z.string() }),
          }),
        ),
      )

      let result = Schema.parseValid({
        type1: 'a',
        type2: 'a',
        aa: 'aa',
        foo: 'foo',
      })

      expect(result).toStrictEqual({ type1: 'a', type2: 'a', aa: 'aa' })

      result = Schema.parseValid({
        type1: 'a',
        type2: 'b',
        ab: 1,
        bar: 1,
      })

      expect(result).toStrictEqual({ type1: 'a', type2: 'b', ab: 1 })

      result = Schema.parseValid({
        type1: 'b',
        type2: 'a',
        ba: true,
        baz: {},
      })

      expect(result).toStrictEqual({ type1: 'b', type2: 'a', ba: true })

      result = Schema.parseValid({
        type1: 'b',
        type2: 'b',
        bb: { qux: 'qux' },
        qux: 'qux',
      })

      expect(result).toStrictEqual({
        type1: 'b',
        type2: 'b',
        bb: { qux: 'qux' },
      })
    })

    it('parses partially valid chained branches recursively', () => {
      const Schema = branch(
        ['type1'],
        branch(
          ['type2'],
          z.object({
            type1: z.literal('a'),
            type2: z.literal('a'),
            aa: z.string(),
          }),
          z.object({
            type1: z.literal('a'),
            type2: z.literal('b'),
            ab: z.number(),
          }),
        ),
        branch(
          ['type2'],
          z.object({
            type1: z.literal('b'),
            type2: z.literal('a'),
            ba: z.boolean(),
          }),
          z.object({
            type1: z.literal('b'),
            type2: z.literal('b'),
            bb: z.object({ qux: z.string() }),
          }),
        ),
      )

      let result = Schema.parseValid({
        type1: 'a',
        type2: 'a',
        aa: 1,
      })

      expect(result).toStrictEqual({ type1: 'a', type2: 'a' })

      result = Schema.parseValid({
        type1: 'a',
        type2: 'b',
        ab: 'ab',
      })

      expect(result).toStrictEqual({ type1: 'a', type2: 'b' })

      result = Schema.parseValid({
        type1: 'a',
        type2: 'c',
        ac: 'ac',
      })

      expect(result).toStrictEqual({ type1: 'a' })

      result = Schema.parseValid({
        type1: 'c',
        type2: 'c',
        ca: 'ca',
      })

      expect(result).toStrictEqual({})

      result = Schema.parseValid({
        type1: 'c',
      })

      expect(result).toStrictEqual({})
    })

    it('parses partially valid chained branches that point to different sub-objects', () => {
      const Schema = branch(
        ['subA', 'type'],
        branch(
          ['subB', 'type'],
          z.object({
            subA: z.object({ type: z.literal('a'), foo: z.string() }),
            subB: z.object({ type: z.literal('a'), bar: z.number() }),
          }),
          z.object({
            subA: z.object({ type: z.literal('a'), foo: z.string() }),
            subB: z.object({ type: z.literal('b'), bar: z.number() }),
          }),
        ),
        branch(
          ['subB', 'type'],
          z.object({
            subA: z.object({ type: z.literal('b'), foo: z.string() }),
            subB: z.object({ type: z.literal('a'), bar: z.number() }),
          }),
          z.object({
            subA: z.object({ type: z.literal('b'), foo: z.string() }),
            subB: z.object({ type: z.literal('b'), bar: z.number() }),
          }),
        ),
      )

      let result = Schema.parseValid({
        subA: { type: 'a', foo: 'foo', qux: 'qux' },
        subB: { type: 'a', bar: 1 },
      })

      expect(result).toStrictEqual({
        subA: { type: 'a', foo: 'foo' },
        subB: { type: 'a', bar: 1 },
      })

      result = Schema.parseValid({
        subA: { type: 'a', foo: 'foo', qux: 'qux' },
        subB: { type: 'a', bar: 'bar' },
      })

      expect(result).toStrictEqual({
        subA: { type: 'a', foo: 'foo' },
        subB: { type: 'a' },
      })

      result = Schema.parseValid({
        subA: { type: 'a', foo: 'foo' },
        subB: { type: 'b', bar: 2, baz: 'baz' },
      })

      expect(result).toStrictEqual({
        subA: { type: 'a', foo: 'foo' },
        subB: { type: 'b', bar: 2 },
      })

      result = Schema.parseValid({
        subA: { type: 'a', foo: 'foo' },
        subB: { type: 'c', bar: 2, baz: 'baz' },
      })

      expect(result).toStrictEqual({
        subA: { type: 'a', foo: 'foo' },
        subB: { bar: 2 },
      })
    })
  })

  describe('clone', () => {
    it('successfully clones', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string() }),
        z.object({ type: z.literal('b'), bar: z.number() }),
      )

      const Clone = Schema.clone()
      expect(JSON.stringify(Clone._def)).toEqual(JSON.stringify(Schema._def))
      expect(Clone).not.toBe(Schema)
    })

    it('successfully clones complex branch structure', () => {
      const Schema = branch(
        ['type1'],
        branch(
          ['type2'],
          z.object({
            type1: z.literal('a'),
            type2: z.literal('a'),
            aa: z.string(),
          }),
          z.object({
            type1: z.literal('a'),
            type2: z.literal('b'),
            ab: z.number(),
          }),
        ),
        branch(
          ['type2'],
          z.object({
            type1: z.literal('b'),
            type2: z.literal('a'),
            ba: z.boolean(),
          }),
          z.object({
            type1: z.literal('b'),
            type2: z.literal('b'),
            bb: z.object({ qux: z.string() }),
          }),
        ),
      )

      const Clone = Schema.clone()
      expect(JSON.stringify(Clone._def)).toEqual(JSON.stringify(Schema._def))
      expect(Clone).not.toBe(Schema)
    })
  })
})

describe('merge', () => {
  it('extends each option of the FormBranch with the provided base ZodObject', () => {
    const Result = merge(
      z.object({ foo: z.string() }),
      branch(
        ['type'],
        z.object({ type: z.literal('a') }),
        z.object({ type: z.literal('b') }),
      ),
    )

    const [First, Second] = [...Result._def.options.values()] as ObjectSchema[]

    expect(First).toBeInstanceOf(ZodObject)
    expect(First.shape).toHaveProperty('foo')
    expect(First.shape.foo).toBeInstanceOf(ZodString)

    expect(Second).toBeInstanceOf(ZodObject)
    expect(Second.shape).toHaveProperty('foo')
    expect(Second.shape.foo).toBeInstanceOf(ZodString)
  })

  it('deep-extends each option of the FormBranch with the provided base ZodObject', () => {
    const Result = merge(
      z.object({
        foo: z.object({
          bar: z.object({
            baz: z.string(),
          }),
        }),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.object({ qux: z.string() }),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.object({
            bar: z.object({
              quy: z.string(),
            }),
          }),
        }),
      ),
    )

    const [First, Second] = [...Result._def.options.values()] as ObjectSchema[]

    expect(First).toBeInstanceOf(ZodObject)
    expect(First.shape).toHaveProperty('foo')
    expect(First.shape.foo).toBeInstanceOf(ZodObject)
    expect((First.shape.foo as ZodObject<any>).shape).toMatchObject({
      bar: expect.any(ZodObject) as unknown,
      qux: expect.any(ZodString) as unknown,
    })

    expect(Second).toBeInstanceOf(ZodObject)
    expect(Second.shape).toHaveProperty('foo')
    expect(Second.shape.foo).toBeInstanceOf(ZodObject)
    expect((Second.shape.foo as ZodObject<any>).shape).toMatchObject({
      bar: expect.any(ZodObject) as unknown,
    })

    if (!(Second.shape.foo instanceof ZodObject)) return
    expect(
      (
        (Second.shape.foo.shape as Record<string, unknown>)
          .bar as ZodObject<any>
      ).shape,
    ).toMatchObject({
      baz: expect.any(ZodString) as unknown,
      quy: expect.any(ZodString) as unknown,
    })
  })

  it('deep-extends common object properties', () => {
    const Result = merge(
      z.object({
        foo: z.object({
          bar: z.string(),
        }),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.object({
            baz: z.number(),
          }),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.object({
            qux: z.number(),
          }),
        }),
      ),
    )

    type Type =
      | { type: 'a'; foo: { bar: string } & { baz: number } }
      | { type: 'b'; foo: { bar: string } & { qux: number } }
    expectType<TypeEqual<z.infer<typeof Result>, Type>>(true)

    const [First, Second] = [...Result._def.options.values()]

    expect(First.shape.foo.shape).toMatchObject({
      bar: expect.any(ZodString) as unknown,
      baz: expect.any(ZodNumber) as unknown,
    })

    expect(Second.shape.foo.shape).toMatchObject({
      bar: expect.any(ZodString) as unknown,
      qux: expect.any(ZodNumber) as unknown,
    })
  })

  it('deep-extends common object properties that are branches in the base', () => {
    const Result = merge(
      z.object({
        foo: branch(
          ['bar'],
          z.object({
            bar: z.literal('a'),
            baz: z.number(),
          }),
          z.object({
            bar: z.literal('b'),
          }),
        ),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.object({
            qux: z.number(),
          }),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.object({
            qux: z.boolean(),
          }),
        }),
      ),
    )

    type BaseBranch = { bar: 'a'; baz: number } | { bar: 'b' }

    type Type =
      | { type: 'a'; foo: { qux: number } & BaseBranch }
      | { type: 'b'; foo: { qux: boolean } & BaseBranch }
    expectType<TypeEqual<z.infer<typeof Result>, Type>>(true)

    const [First, Second] = [...Result._def.options.values()]

    /**
     * Expectations for the First option
     */
    expect(First.shape.foo).toBeInstanceOf(FormBranch)

    if (!(First.shape.foo instanceof FormBranch)) return

    const [OptionA1, OptionB1] = [...First.shape.foo._def.options.values()]

    /**
     * Expectations for the Option A of the First option
     */
    expect(OptionA1).toBeInstanceOf(ZodObject)
    if (!(OptionA1 instanceof ZodObject)) return

    expect(OptionA1.shape).toMatchObject({
      qux: expect.any(ZodNumber) as unknown,
      bar: expect.any(ZodLiteral) as unknown,
      baz: expect.any(ZodNumber) as unknown,
    })

    if (!(OptionA1.shape.bar instanceof ZodLiteral)) return

    expect(OptionA1.shape.bar._def.value).toBe('a')

    /**
     * Expectations for the Option B of the First option
     */
    expect(OptionB1).toBeInstanceOf(ZodObject)
    if (!(OptionB1 instanceof ZodObject)) return

    expect(OptionB1.shape).toMatchObject({
      qux: expect.any(ZodNumber) as unknown,
      bar: expect.any(ZodLiteral) as unknown,
    })

    if (!(OptionB1.shape.bar instanceof ZodLiteral)) return

    expect(OptionB1.shape.bar._def.value).toBe('b')

    /**
     * Expectations for the Second option
     */
    expect(Second.shape.foo).toBeInstanceOf(FormBranch)

    if (!(Second.shape.foo instanceof FormBranch)) return

    const [OptionA2, OptionB2] = [...Second.shape.foo._def.options.values()]

    /**
     * Expectations for the Option A of the Second option
     */
    expect(OptionA2).toBeInstanceOf(ZodObject)
    if (!(OptionA2 instanceof ZodObject)) return

    expect(OptionA2.shape).toMatchObject({
      qux: expect.any(ZodBoolean) as unknown,
      bar: expect.any(ZodLiteral) as unknown,
      baz: expect.any(ZodNumber) as unknown,
    })

    if (!(OptionA2.shape.bar instanceof ZodLiteral)) return

    expect(OptionA2.shape.bar._def.value).toBe('a')

    /**
     * Expectations for the Option B of the Second option
     */
    expect(OptionB2).toBeInstanceOf(ZodObject)
    if (!(OptionB2 instanceof ZodObject)) return

    expect(OptionB2.shape).toMatchObject({
      qux: expect.any(ZodBoolean) as unknown,
      bar: expect.any(ZodLiteral) as unknown,
    })

    if (!(OptionB2.shape.bar instanceof ZodLiteral)) return

    expect(OptionB2.shape.bar._def.value).toBe('b')
  })

  it('deep-extends common object properties that are branches in the base', () => {
    const Result = merge(
      z.object({
        foo: z.object({
          qux: z.number(),
        }),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: branch(
            ['bar'],
            z.object({
              bar: z.literal('a'),
              baz: z.number(),
            }),
            z.object({
              bar: z.literal('b'),
            }),
          ),
        }),
        z.object({
          type: z.literal('b'),
          foo: branch(
            ['bar'],
            z.object({
              bar: z.literal('a'),
              baz: z.boolean(),
            }),
            z.object({
              bar: z.literal('b'),
            }),
          ),
        }),
      ),
    )

    type BaseBranch<Baz> = { bar: 'a'; baz: Baz } | { bar: 'b' }

    type Type =
      | { type: 'a'; foo: { qux: number } & BaseBranch<number> }
      | { type: 'b'; foo: { qux: number } & BaseBranch<boolean> }
    expectType<TypeEqual<z.infer<typeof Result>, Type>>(true)

    const [First, Second] = [...Result._def.options.values()]

    /**
     * Expectations for the First option
     */
    expect(First.shape.foo).toBeInstanceOf(FormBranch)
    if (!(First.shape.foo instanceof FormBranch)) return

    const [OptionA1, OptionB1] = [...First.shape.foo._def.options.values()]

    /**
     * Expectations for the Option A of the First option
     */
    expect(OptionA1).toBeInstanceOf(ZodObject)
    if (!(OptionA1 instanceof ZodObject)) return

    expect(OptionA1.shape).toMatchObject({
      qux: expect.any(ZodNumber) as unknown,
      bar: expect.any(ZodLiteral) as unknown,
      baz: expect.any(ZodNumber) as unknown,
    })

    if (!(OptionA1.shape.bar instanceof ZodLiteral)) return

    expect(OptionA1.shape.bar._def.value).toBe('a')

    /**
     * Expectations for the Option B of the First option
     */
    expect(OptionB1).toBeInstanceOf(ZodObject)
    if (!(OptionB1 instanceof ZodObject)) return

    expect(OptionB1.shape).toMatchObject({
      qux: expect.any(ZodNumber) as unknown,
      bar: expect.any(ZodLiteral) as unknown,
    })

    if (!(OptionB1.shape.bar instanceof ZodLiteral)) return

    expect(OptionB1.shape.bar._def.value).toBe('b')

    /**
     * Expectations for the Second option
     */
    expect(Second.shape.foo).toBeInstanceOf(FormBranch)

    if (!(Second.shape.foo instanceof FormBranch)) return

    const [OptionA2, OptionB2] = [...Second.shape.foo._def.options.values()]

    /**
     * Expectations for the Option A of the Second option
     */
    expect(OptionA2).toBeInstanceOf(ZodObject)
    if (!(OptionA2 instanceof ZodObject)) return

    expect(OptionA2.shape).toMatchObject({
      qux: expect.any(ZodNumber) as unknown,
      bar: expect.any(ZodLiteral) as unknown,
      baz: expect.any(ZodBoolean) as unknown,
    })

    if (!(OptionA2.shape.bar instanceof ZodLiteral)) return

    expect(OptionA2.shape.bar._def.value).toBe('a')

    /**
     * Expectations for the Option B of the Second option
     */
    expect(OptionB2).toBeInstanceOf(ZodObject)
    if (!(OptionB2 instanceof ZodObject)) return

    expect(OptionB2.shape).toMatchObject({
      qux: expect.any(ZodNumber) as unknown,
      bar: expect.any(ZodLiteral) as unknown,
    })

    if (!(OptionB2.shape.bar instanceof ZodLiteral)) return

    expect(OptionB2.shape.bar._def.value).toBe('b')
  })

  it('keeps properties unique to the base', () => {
    const Result = merge(
      z.object({
        foo: z.object({
          bar: z.string(),
        }),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.object({
            baz: z.number(),
          }),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.object({
            qux: z.number(),
          }),
        }),
      ),
    )

    const [First, Second] = [...Result._def.options.values()]

    expect(First.shape.foo.shape).toMatchObject({
      bar: expect.any(ZodString) as unknown,
    })

    expect(Second.shape.foo.shape).toMatchObject({
      bar: expect.any(ZodString) as unknown,
    })
  })

  it('keeps properties unique to the options', () => {
    const Result = merge(
      z.object({
        foo: z.object({
          bar: z.string(),
        }),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.object({
            baz: z.number(),
          }),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.object({
            qux: z.number(),
          }),
        }),
      ),
    )

    const [First, Second] = [...Result._def.options.values()]

    expect(First.shape.foo.shape).toMatchObject({
      baz: expect.any(ZodNumber) as unknown,
    })

    expect(Second.shape.foo.shape).toMatchObject({
      qux: expect.any(ZodNumber) as unknown,
    })
  })

  it('keeps base object property if same option property is not object or never', () => {
    const Result = merge(
      z.object({
        foo: z.object({
          bar: z.string(),
        }),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.string(),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.number(),
        }),
      ),
    )

    const [First, Second] = [...Result._def.options.values()]

    expect(First.shape.foo).toBeInstanceOf(ZodObject)
    expect(First.shape.foo.shape).toMatchObject({
      bar: expect.any(ZodString) as unknown,
    })

    expect(Second.shape.foo).toBeInstanceOf(ZodObject)
    expect(Second.shape.foo.shape).toMatchObject({
      bar: expect.any(ZodString) as unknown,
    })
  })

  it('keeps options object property if same base property is not object', () => {
    const Result = merge(
      z.object({
        foo: z.string(),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.object({
            baz: z.number(),
          }),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.object({
            qux: z.number(),
          }),
        }),
      ),
    )

    const [First, Second] = [...Result._def.options.values()]

    expect(First.shape.foo).toBeInstanceOf(ZodObject)
    expect(First.shape.foo.shape).toMatchObject({
      baz: expect.any(ZodNumber) as unknown,
    })

    expect(Second.shape.foo).toBeInstanceOf(ZodObject)
    expect(Second.shape.foo.shape).toMatchObject({
      qux: expect.any(ZodNumber) as unknown,
    })
  })

  it('keeps base object property if neither property is object', () => {
    const Result = merge(
      z.object({
        foo: z.string(),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.number(),
        }),
        z.object({
          type: z.literal('b'),
        }),
      ),
    )

    const [First, Second] = [...Result._def.options.values()]

    expect(First.shape.foo).toBeInstanceOf(ZodString)
    expect(Second.shape.foo).toBeInstanceOf(ZodString)
  })

  it('sets Remove if a common property is defined as Remove in the option', () => {
    const Result = merge(
      z.object({
        foo: z.object({ bar: z.string() }),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: Remove,
        }),
        z.object({
          type: z.literal('b'),
        }),
      ),
    )

    const [First, Second] = [...Result._def.options.values()]

    expect(First.shape.foo).toBe(Remove)
    expect(Second.shape.foo).toBeInstanceOf(ZodObject)
  })

  it('sets Remove if a nested property is defined as Remove in the option', () => {
    const Result = merge(
      z.object({
        foo: z.object({
          bar: z.string(),
        }),
      }),
      branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.object({
            bar: Remove,
          }),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.object({}),
        }),
      ),
    )

    const [First, Second] = [...Result._def.options.values()]

    expect(First.shape.foo.shape.bar).toBe(Remove)
    expect(Second.shape.foo.shape.bar).toBeInstanceOf(ZodString)
  })
})

describe('mergeAll', () => {
  it('merges a list of branches', () => {
    const BranchA = branch(
      ['typeA'],
      z.object({
        typeA: z.literal('a1'),
        foo: z.string(),
      }),
      z.object({
        typeA: z.literal('a2'),
        bar: z.number(),
      }),
    )

    const BranchB = branch(
      ['typeB'],
      z.object({
        typeB: z.literal('b1'),
        qux: z.string(),
      }),
      z.object({
        typeB: z.literal('b2'),
        baz: z.number(),
      }),
    )

    const Result = mergeAll(BranchA, BranchB)

    expect(Result.options()).toHaveLength(2)

    const [B1, B2] = [...Result.options()]

    expectInstanceOf(B1, FormBranch)
    expectInstanceOf(B2, FormBranch)

    expect(B1.options()).toHaveLength(2)
    expect(B2.options()).toHaveLength(2)

    const [O1, O2, O3, O4] = [...B1.options(), ...B2.options()]

    expectInstanceOf(O1, ZodObject)
    expectInstanceOf(O2, ZodObject)
    expectInstanceOf(O3, ZodObject)
    expectInstanceOf(O4, ZodObject)

    expect(O1.shape).toMatchObject({
      ...BranchA.options()[0].shape,
      ...BranchB.options()[0].shape,
    })

    expect(O2.shape).toMatchObject({
      ...BranchA.options()[0].shape,
      ...BranchB.options()[1].shape,
    })

    expect(O3.shape).toMatchObject({
      ...BranchA.options()[1].shape,
      ...BranchB.options()[0].shape,
    })

    expect(O4.shape).toMatchObject({
      ...BranchA.options()[1].shape,
      ...BranchB.options()[1].shape,
    })
  })

  it('merges branches with nested branches', () => {
    const BranchA = branch(
      ['typeA'],
      z.object({
        typeA: z.literal('a1'),
        foo: z.string(),
      }),
      z.object({
        typeA: z.literal('a2'),
        bar: z.number(),
      }),
    )

    const BranchB = branch(
      ['typeB'],
      branch(
        ['typeC'],
        z.object({
          typeB: z.literal('b1'),
          typeC: z.literal('c1'),
          qux: z.string(),
        }),
        z.object({
          typeB: z.literal('b2'),
          typeC: z.literal('c2'),
          baz: z.number(),
        }),
      ),
      branch(
        ['typeD'],
        z.object({
          typeB: z.literal('b1'),
          typeD: z.literal('d1'),
          qux: z.string(),
        }),
        z.object({
          typeB: z.literal('b2'),
          typeD: z.literal('d2'),
          baz: z.number(),
        }),
      ),
    )

    const Result = mergeAll(BranchA, BranchB)

    expect(Result.options()).toHaveLength(2)

    const [B1, B2] = [...Result.options()]

    expectInstanceOf(B1, FormBranch)
    expectInstanceOf(B2, FormBranch)

    expect(B1.options()).toHaveLength(2)
    expect(B2.options()).toHaveLength(2)

    const [B11, B12] = [...B1.options()]
    const [B21, B22] = [...B2.options()]

    expectInstanceOf(B11, FormBranch)
    expectInstanceOf(B12, FormBranch)
    expectInstanceOf(B21, FormBranch)
    expectInstanceOf(B22, FormBranch)

    expect(B11.options()).toHaveLength(2)
    expect(B12.options()).toHaveLength(2)
    expect(B21.options()).toHaveLength(2)
    expect(B22.options()).toHaveLength(2)

    const [O1, O2, O3, O4, O5, O6, O7, O8] = [
      ...B11.options(),
      ...B12.options(),
      ...B21.options(),
      ...B22.options(),
    ]

    expectInstanceOf(O1, ZodObject)
    expectInstanceOf(O2, ZodObject)
    expectInstanceOf(O3, ZodObject)
    expectInstanceOf(O4, ZodObject)
    expectInstanceOf(O5, ZodObject)
    expectInstanceOf(O6, ZodObject)
    expectInstanceOf(O7, ZodObject)
    expectInstanceOf(O8, ZodObject)

    expect(O1.shape).toMatchObject({
      ...BranchA.options()[0].shape,
      ...BranchB.options()[0].options()[0].shape,
    })

    expect(O2.shape).toMatchObject({
      ...BranchA.options()[0].shape,
      ...BranchB.options()[0].options()[1].shape,
    })

    expect(O3.shape).toMatchObject({
      ...BranchA.options()[0].shape,
      ...BranchB.options()[1].options()[0].shape,
    })

    expect(O4.shape).toMatchObject({
      ...BranchA.options()[0].shape,
      ...BranchB.options()[1].options()[1].shape,
    })

    expect(O5.shape).toMatchObject({
      ...BranchA.options()[1].shape,
      ...BranchB.options()[0].options()[0].shape,
    })

    expect(O6.shape).toMatchObject({
      ...BranchA.options()[1].shape,
      ...BranchB.options()[0].options()[1].shape,
    })

    expect(O7.shape).toMatchObject({
      ...BranchA.options()[1].shape,
      ...BranchB.options()[1].options()[0].shape,
    })

    expect(O8.shape).toMatchObject({
      ...BranchA.options()[1].shape,
      ...BranchB.options()[1].options()[1].shape,
    })
  })
})
