import { useShowViewMore } from '@/app/hooks/useShowViewMore'
import { useI18n } from '@gatx-corp/platform-one-common'
import { AccordionHandle as QuestionHandle } from '@gatx-corp/platform-one-common/components/Accordion'
import { Button } from '@gatx-corp/platform-one-common/components/Button'
import { Option } from '@gatx-corp/platform-one-common/components/ChoiceGroup'
import {
  Heading,
  Landmark,
} from '@gatx-corp/platform-one-common/components/Content'
import { DropDown } from '@gatx-corp/platform-one-common/components/DropDown'
import {
  cloneElement,
  createContext,
  forwardRef,
  use,
  useMemo,
  useRef,
} from 'react'
import { ButtonProps as BootstrapButtonProps } from 'react-bootstrap'

import { Filter, FILTERS, useFilter } from '@/features/forms/hooks/useFilter'
import { useCurrentFormDraft } from '@/features/forms/providers/FormDraftProvider'
import clsx from 'clsx'
import { FormLayoutProps } from '../../../components/FormLayout'
import RelatedForms from '../../../components/RelatedForms'
import {
  Form,
  FormContent,
  FormQuestion,
  FormResponse,
  FormStatusCode,
} from '../../../types/Form'

/**
 * A defect in a Inbound Inspection form is related to a pattern
 * associated with an array schema. This means the data itself be an array.
 */
function isDefect(question: FormQuestion, response: FormResponse) {
  return Array.isArray(response[question.id].data)
}

function isIVP(question: FormQuestion) {
  return !!question.mrAttributes && question.mrAttributes.length > 0
}

type ExclusionFilter = (
  questions: FormQuestion[],
  draft?: FormContent,
) => FormQuestion[]

const EXCLUSION_FILTERS: Record<Filter, ExclusionFilter> = {
  defects: (questions, draft) => {
    if (!draft) return questions
    return questions.filter((q) => !isDefect(q, draft.response))
  },

  incomplete: (questions, draft) => {
    if (!draft) return []
    return questions.filter((q) => !!draft.signatures?.[q.id])
  },

  IVPs: (questions) => {
    return questions.filter((q) => !isIVP(q))
  },

  all: () => [],
}

function getHiddenQuestions(form: Form, filter: Filter, draft?: FormContent) {
  const excludedQuestions = EXCLUSION_FILTERS[filter](form.questions, draft)
  return excludedQuestions.map((q) => q.id)
}

function useHiddenQuestionIds(form: Form, filter: Filter) {
  const [draft] = useCurrentFormDraft()

  return useMemo(
    () => new Set(getHiddenQuestions(form, filter, draft)),
    [form, filter, draft],
  )
}

const FilterContext = createContext<Filter>('all')

const FilterToggle = forwardRef<
  HTMLButtonElement,
  Omit<BootstrapButtonProps, 'as'>
>(function FilterToggle(props, ref) {
  const { t } = useI18n('Forms', { keyPrefix: 'Inbound Inspection' })
  const filter = use(FilterContext)

  return (
    <button
      {...props}
      ref={ref}
      className={clsx(
        'w-[137px] flex gap-sm justify-between items-center',
        'px-md py-sm rounded',
        'bg-[white] hover:bg-light-3',
        'dark:bg-dark-9 dark:hover:bg-dark-10',
        'focus:ring-4 focus:ring-[rgba(71,_179,_255,_0.4)]',
      )}
    >
      {t(`filter.option.${filter}`)}
      <span className="pointer-events-none">{props.children}</span>
    </button>
  )
})

function getFirstVisibleQuestionId(
  form: Form,
  hiddenQuestionIds: Set<FormQuestion['id']>,
) {
  return form.questions.findIndex((q) => !hiddenQuestionIds.has(q.id))
}

const InboundInspectionLayout = ({ children, form }: FormLayoutProps) => {
  const { t } = useI18n('Forms', { keyPrefix: 'Inbound Inspection' })
  const { state, refs } = useShowViewMore()

  const [filter, setFilter] = useFilter()
  const [draft] = useCurrentFormDraft()
  const hiddenQuestionIds = useHiddenQuestionIds(form, filter)
  const questionHandle = useRef<QuestionHandle>(null)

  /**
   * Needs to be memoized because the Select component is not robust to passing
   * an unstable reference to the items prop.
   */
  const filterOptions = useMemo(() => {
    return FILTERS.map((value) => ({
      label: t(`filter.option.${value}`),
      value,
    }))
  }, [t])

  function onFilterChange(item: Option<Filter>) {
    setFilter(item.value)

    const hiddenQuestionIds = new Set(
      getHiddenQuestions(form, item.value, draft),
    )
    const firstVisibleQuestionId = getFirstVisibleQuestionId(
      form,
      hiddenQuestionIds,
    )

    questionHandle.current?.expand(firstVisibleQuestionId)
  }

  function getFilterCount(form: Form, draft?: FormContent) {
    const isActive =
      form.statusCode === FormStatusCode.IN_PROCESS ||
      form.statusCode === FormStatusCode.SIGNED
    if (isActive || filter === 'IVPs') {
      const hiddenCount = getHiddenQuestions(form, filter, draft).length
      return form.questions.length - hiddenCount
    }
    return 0
  }

  const items = filterOptions.map((option) => ({
    label: option.label,
    value: option.value,
    onClick: () => onFilterChange(option),
  }))

  return (
    <Landmark>
      <div className="flex flex-row items-center justify-between gap-xl">
        <Heading className="text-title-sm ml-lg">{t('title')}</Heading>
        <div className="flex items-center gap-md">
          <Button
            ref={refs.handleRef}
            className="whitespace-nowrap"
            type="button"
            size="small"
            variant="text"
            text={t('toggle.zones', { context: state })}
          />
          <label className="flex gap-md items-center">
            <span>{t('filter.label')}:</span>
            <FilterContext.Provider value={filter}>
              <DropDown.ToggleMenu
                label={t('filter.label')}
                labelScreenreaderOnly
                Toggle={FilterToggle}
              >
                {items.map((option) => (
                  <DropDown.Item key={option.value} {...option} />
                ))}
              </DropDown.ToggleMenu>
            </FilterContext.Provider>
          </label>
        </div>
      </div>
      <div ref={refs.targetRef} className="my-xl">
        <RelatedForms
          form={form}
          getCount={filter !== 'all' ? getFilterCount : undefined}
        />
      </div>

      {children &&
        cloneElement(children, { hiddenQuestionIds, questionHandle })}
    </Landmark>
  )
}

export default InboundInspectionLayout
